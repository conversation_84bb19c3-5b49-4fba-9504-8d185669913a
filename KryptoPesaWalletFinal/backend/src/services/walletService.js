const Wallet = require('../models/Wallet');
const WalletUtils = require('../../shared/utils/walletUtils');
const ethereumService = require('./blockchain/ethereumService');
const logger = require('../utils/logger');
const { AppError } = require('../middleware/errorHandler');

class WalletService {
  
  /**
   * Create a new wallet for user
   */
  async createWallet(userId, mnemonic = null) {
    try {
      // Check if user already has a wallet
      const existingWallet = await Wallet.findOne({ user: userId });
      if (existingWallet) {
        throw new AppError('User already has a wallet', 400);
      }
      
      // Generate mnemonic if not provided
      if (!mnemonic) {
        mnemonic = WalletUtils.generateMnemonic();
      } else {
        // Validate provided mnemonic
        if (!WalletUtils.validateMnemonic(mnemonic)) {
          throw new AppError('Invalid mnemonic phrase', 400);
        }
      }
      
      // Create Ethereum wallet
      const ethWallet = WalletUtils.createEthereumWallet(mnemonic);
      
      // Create Bitcoin wallet
      const btcWallet = WalletUtils.createBitcoinWallet(mnemonic);
      
      // Hash mnemonic for verification (don't store the actual mnemonic)
      const mnemonicHash = WalletUtils.hashMnemonic(mnemonic);
      
      // Create wallet document
      const wallet = new Wallet({
        user: userId,
        addresses: {
          ethereum: {
            address: ethWallet.address,
            publicKey: ethWallet.publicKey,
            derivationPath: ethWallet.derivationPath
          },
          bitcoin: {
            address: btcWallet.address,
            publicKey: btcWallet.publicKey,
            derivationPath: btcWallet.derivationPath
          }
        },
        security: {
          mnemonicHash
        },
        balances: [
          // Initialize with zero balances for common tokens
          { symbol: 'MATIC', network: 'polygon', balance: '0', decimals: 18 },
          { symbol: 'USDT', network: 'polygon', balance: '0', decimals: 6, contractAddress: process.env.USDT_CONTRACT_ADDRESS },
          { symbol: 'USDC', network: 'polygon', balance: '0', decimals: 6, contractAddress: process.env.USDC_CONTRACT_ADDRESS },
          { symbol: 'ETH', network: 'ethereum', balance: '0', decimals: 18 },
          { symbol: 'BTC', network: 'bitcoin', balance: '0', decimals: 8 }
        ]
      });
      
      await wallet.save();
      
      logger.info(`Wallet created for user ${userId}: ETH ${ethWallet.address}, BTC ${btcWallet.address}`);
      
      return {
        wallet,
        mnemonic, // Return mnemonic only once during creation
        addresses: {
          ethereum: ethWallet.address,
          bitcoin: btcWallet.address
        }
      };
      
    } catch (error) {
      logger.error(`Error creating wallet: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Import existing wallet from mnemonic
   */
  async importWallet(userId, mnemonic, accountIndex = 0) {
    try {
      // Validate mnemonic
      if (!WalletUtils.validateMnemonic(mnemonic)) {
        throw new AppError('Invalid mnemonic phrase', 400);
      }
      
      // Check if user already has a wallet
      const existingWallet = await Wallet.findOne({ user: userId });
      if (existingWallet) {
        throw new AppError('User already has a wallet', 400);
      }
      
      return await this.createWallet(userId, mnemonic);
      
    } catch (error) {
      logger.error(`Error importing wallet: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Get wallet for user
   */
  async getWallet(userId) {
    try {
      const wallet = await Wallet.findOne({ user: userId }).populate('user', 'username email');
      
      if (!wallet) {
        throw new AppError('Wallet not found', 404);
      }
      
      return wallet;
      
    } catch (error) {
      logger.error(`Error getting wallet: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Update wallet balances
   */
  async updateBalances(userId) {
    try {
      const wallet = await this.getWallet(userId);
      
      // Update Ethereum/Polygon balances
      const ethAddress = wallet.addresses.ethereum.address;
      
      // Update MATIC balance
      const maticBalance = await ethereumService.getNativeBalance(ethAddress, 'polygon');
      await wallet.updateBalance('MATIC', 'polygon', maticBalance.balance);
      
      // Update USDT balance
      if (process.env.USDT_CONTRACT_ADDRESS) {
        const usdtBalance = await ethereumService.getTokenBalance(
          process.env.USDT_CONTRACT_ADDRESS,
          ethAddress,
          'polygon'
        );
        await wallet.updateBalance('USDT', 'polygon', usdtBalance.balance, process.env.USDT_CONTRACT_ADDRESS);
      }
      
      // Update USDC balance
      if (process.env.USDC_CONTRACT_ADDRESS) {
        const usdcBalance = await ethereumService.getTokenBalance(
          process.env.USDC_CONTRACT_ADDRESS,
          ethAddress,
          'polygon'
        );
        await wallet.updateBalance('USDC', 'polygon', usdcBalance.balance, process.env.USDC_CONTRACT_ADDRESS);
      }
      
      // Update ETH balance
      const ethBalance = await ethereumService.getNativeBalance(ethAddress, 'ethereum');
      await wallet.updateBalance('ETH', 'ethereum', ethBalance.balance);
      
      // TODO: Update Bitcoin balance using Bitcoin service
      
      logger.info(`Balances updated for wallet ${wallet.addresses.ethereum.address}`);
      
      return await this.getWallet(userId);
      
    } catch (error) {
      logger.error(`Error updating balances: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Get transaction history
   */
  async getTransactionHistory(userId, limit = 50, offset = 0) {
    try {
      const wallet = await this.getWallet(userId);
      
      const transactions = wallet.transactions
        .sort((a, b) => b.timestamp - a.timestamp)
        .slice(offset, offset + limit);
      
      return {
        transactions,
        total: wallet.transactions.length,
        hasMore: offset + limit < wallet.transactions.length
      };
      
    } catch (error) {
      logger.error(`Error getting transaction history: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Add transaction to wallet history
   */
  async addTransaction(userId, transactionData) {
    try {
      const wallet = await this.getWallet(userId);
      await wallet.addTransaction(transactionData);
      
      logger.info(`Transaction added to wallet ${wallet.addresses.ethereum.address}: ${transactionData.hash}`);
      
      return wallet;
      
    } catch (error) {
      logger.error(`Error adding transaction: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Update transaction status
   */
  async updateTransactionStatus(userId, txHash, status, confirmations = 0, blockNumber = null) {
    try {
      const wallet = await this.getWallet(userId);
      await wallet.updateTransactionStatus(txHash, status, confirmations, blockNumber);
      
      logger.info(`Transaction status updated: ${txHash} -> ${status}`);
      
      return wallet;
      
    } catch (error) {
      logger.error(`Error updating transaction status: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Verify mnemonic phrase
   */
  async verifyMnemonic(userId, mnemonic) {
    try {
      const wallet = await this.getWallet(userId);
      const mnemonicHash = WalletUtils.hashMnemonic(mnemonic);
      
      return wallet.security.mnemonicHash === mnemonicHash;
      
    } catch (error) {
      logger.error(`Error verifying mnemonic: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Mark backup as completed
   */
  async markBackupCompleted(userId) {
    try {
      const wallet = await this.getWallet(userId);
      
      wallet.security.backupCompleted = true;
      wallet.security.backupDate = new Date();
      
      await wallet.save();
      
      logger.info(`Backup marked as completed for wallet ${wallet.addresses.ethereum.address}`);
      
      return wallet;
      
    } catch (error) {
      logger.error(`Error marking backup completed: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Get wallet statistics
   */
  async getWalletStats(userId) {
    try {
      const wallet = await this.getWallet(userId);
      
      // Calculate total portfolio value (simplified)
      let totalValue = 0;
      for (const balance of wallet.balances) {
        // This would use real price feeds in production
        const amount = parseFloat(WalletUtils.formatTokenAmount(balance.balance, balance.decimals));
        totalValue += amount; // Simplified calculation
      }
      
      const stats = {
        totalValue,
        totalTransactions: wallet.transactions.length,
        successfulTransactions: wallet.transactions.filter(tx => tx.status === 'confirmed').length,
        pendingTransactions: wallet.transactions.filter(tx => tx.status === 'pending').length,
        lastActivity: wallet.transactions.length > 0 ? wallet.transactions[0].timestamp : wallet.createdAt,
        backupCompleted: wallet.security.backupCompleted
      };
      
      return stats;
      
    } catch (error) {
      logger.error(`Error getting wallet stats: ${error.message}`);
      throw error;
    }
  }
}

module.exports = new WalletService();
