const jwt = require('jsonwebtoken');
const User = require('../models/User');
const logger = require('../utils/logger');

const socketHandler = (io) => {
  // Middleware for socket authentication
  io.use(async (socket, next) => {
    try {
      const token = socket.handshake.auth.token;
      
      if (!token) {
        return next(new Error('Authentication error'));
      }
      
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      const user = await User.findById(decoded.userId);
      
      if (!user) {
        return next(new Error('User not found'));
      }
      
      socket.userId = user._id.toString();
      socket.user = user;
      next();
    } catch (error) {
      next(new Error('Authentication error'));
    }
  });

  io.on('connection', (socket) => {
    logger.info(`User connected: ${socket.user.username} (${socket.userId})`);
    
    // Join user to their personal room
    socket.join(`user:${socket.userId}`);
    
    // Handle joining trade rooms
    socket.on('join_trade', (tradeId) => {
      socket.join(`trade:${tradeId}`);
      logger.info(`User ${socket.user.username} joined trade room: ${tradeId}`);
    });
    
    // Handle leaving trade rooms
    socket.on('leave_trade', (tradeId) => {
      socket.leave(`trade:${tradeId}`);
      logger.info(`User ${socket.user.username} left trade room: ${tradeId}`);
    });
    
    // Handle chat messages
    socket.on('send_message', (data) => {
      const { tradeId, message } = data;
      
      // Broadcast to trade room
      socket.to(`trade:${tradeId}`).emit('new_message', {
        tradeId,
        message,
        sender: {
          id: socket.userId,
          username: socket.user.username
        },
        timestamp: new Date()
      });
    });
    
    // Handle typing indicators
    socket.on('typing_start', (tradeId) => {
      socket.to(`trade:${tradeId}`).emit('user_typing', {
        userId: socket.userId,
        username: socket.user.username
      });
    });
    
    socket.on('typing_stop', (tradeId) => {
      socket.to(`trade:${tradeId}`).emit('user_stopped_typing', {
        userId: socket.userId
      });
    });
    
    // Handle disconnection
    socket.on('disconnect', () => {
      logger.info(`User disconnected: ${socket.user.username} (${socket.userId})`);
    });
  });
};

module.exports = socketHandler;
