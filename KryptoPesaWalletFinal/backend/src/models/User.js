const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  username: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    minlength: 3,
    maxlength: 30,
    match: /^[a-zA-Z0-9_]+$/
  },
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true,
    match: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  },
  phone: {
    type: String,
    required: true,
    unique: true,
    match: /^\+[1-9]\d{1,14}$/
  },
  password: {
    type: String,
    required: true,
    minlength: 8
  },
  profile: {
    firstName: {
      type: String,
      required: true,
      trim: true,
      maxlength: 50
    },
    lastName: {
      type: String,
      required: true,
      trim: true,
      maxlength: 50
    },
    avatar: {
      type: String,
      default: null
    },
    bio: {
      type: String,
      maxlength: 500,
      default: ''
    },
    location: {
      country: {
        type: String,
        required: true,
        enum: ['KE', 'TZ', 'UG', 'RW'] // Kenya, Tanzania, Uganda, Rwanda
      },
      city: {
        type: String,
        required: true,
        trim: true
      }
    },
    preferredLanguage: {
      type: String,
      enum: ['en', 'sw'], // English, Swahili
      default: 'en'
    }
  },
  verification: {
    email: {
      verified: {
        type: Boolean,
        default: false
      },
      token: String,
      expiresAt: Date
    },
    phone: {
      verified: {
        type: Boolean,
        default: false
      },
      code: String,
      expiresAt: Date
    },
    identity: {
      verified: {
        type: Boolean,
        default: false
      },
      documentType: {
        type: String,
        enum: ['national_id', 'passport', 'driving_license']
      },
      documentNumber: String,
      documentImages: [String],
      verifiedAt: Date,
      verifiedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      }
    }
  },
  reputation: {
    score: {
      type: Number,
      default: 0,
      min: 0,
      max: 100
    },
    totalTrades: {
      type: Number,
      default: 0
    },
    completedTrades: {
      type: Number,
      default: 0
    },
    cancelledTrades: {
      type: Number,
      default: 0
    },
    disputedTrades: {
      type: Number,
      default: 0
    },
    averageRating: {
      type: Number,
      default: 0,
      min: 0,
      max: 5
    },
    totalRatings: {
      type: Number,
      default: 0
    }
  },
  preferences: {
    notifications: {
      email: {
        type: Boolean,
        default: true
      },
      push: {
        type: Boolean,
        default: true
      },
      sms: {
        type: Boolean,
        default: false
      }
    },
    trading: {
      autoAcceptOffers: {
        type: Boolean,
        default: false
      },
      maxTradeAmount: {
        type: Number,
        default: 50000 // USD equivalent
      },
      preferredPaymentMethods: [{
        type: String,
        enum: ['bank_transfer', 'mobile_money', 'cash', 'other']
      }]
    }
  },
  security: {
    twoFactorEnabled: {
      type: Boolean,
      default: false
    },
    twoFactorSecret: String,
    lastLogin: Date,
    loginAttempts: {
      type: Number,
      default: 0
    },
    lockUntil: Date,
    passwordResetToken: String,
    passwordResetExpires: Date
  },
  status: {
    type: String,
    enum: ['active', 'suspended', 'banned', 'pending'],
    default: 'pending'
  },
  role: {
    type: String,
    enum: ['user', 'admin', 'moderator'],
    default: 'user'
  },
  lastActive: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
userSchema.index({ email: 1 });
userSchema.index({ username: 1 });
userSchema.index({ phone: 1 });
userSchema.index({ 'profile.location.country': 1 });
userSchema.index({ 'reputation.score': -1 });
userSchema.index({ status: 1 });
userSchema.index({ createdAt: -1 });

// Virtual for account lock status
userSchema.virtual('isLocked').get(function() {
  return !!(this.security.lockUntil && this.security.lockUntil > Date.now());
});

// Pre-save middleware to hash password
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(parseInt(process.env.BCRYPT_ROUNDS) || 12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Method to compare password
userSchema.methods.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

// Method to increment login attempts
userSchema.methods.incLoginAttempts = function() {
  if (this.security.lockUntil && this.security.lockUntil < Date.now()) {
    return this.updateOne({
      $unset: { 'security.lockUntil': 1, 'security.loginAttempts': 1 }
    });
  }
  
  const updates = { $inc: { 'security.loginAttempts': 1 } };
  
  if (this.security.loginAttempts + 1 >= 5 && !this.isLocked) {
    updates.$set = {
      'security.lockUntil': Date.now() + 2 * 60 * 60 * 1000 // 2 hours
    };
  }
  
  return this.updateOne(updates);
};

// Method to calculate reputation score
userSchema.methods.calculateReputationScore = function() {
  const { completedTrades, cancelledTrades, disputedTrades, averageRating } = this.reputation;
  
  if (completedTrades === 0) return 0;
  
  const completionRate = completedTrades / (completedTrades + cancelledTrades + disputedTrades);
  const ratingScore = averageRating / 5; // Normalize to 0-1
  
  // Weighted score: 70% completion rate, 30% average rating
  const score = (completionRate * 0.7 + ratingScore * 0.3) * 100;
  
  return Math.round(Math.min(score, 100));
};

// Method to update reputation
userSchema.methods.updateReputation = function(tradeCompleted = false, tradeCancelled = false, tradeDisputed = false, rating = null) {
  if (tradeCompleted) {
    this.reputation.completedTrades += 1;
    this.reputation.totalTrades += 1;
  }
  
  if (tradeCancelled) {
    this.reputation.cancelledTrades += 1;
    this.reputation.totalTrades += 1;
  }
  
  if (tradeDisputed) {
    this.reputation.disputedTrades += 1;
  }
  
  if (rating !== null && rating >= 1 && rating <= 5) {
    const currentTotal = this.reputation.averageRating * this.reputation.totalRatings;
    this.reputation.totalRatings += 1;
    this.reputation.averageRating = (currentTotal + rating) / this.reputation.totalRatings;
  }
  
  this.reputation.score = this.calculateReputationScore();
  
  return this.save();
};

module.exports = mongoose.model('User', userSchema);
