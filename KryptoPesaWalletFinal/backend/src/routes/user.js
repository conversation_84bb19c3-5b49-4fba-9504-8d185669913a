const express = require('express');
const { body, validationResult, query } = require('express-validator');
const { verifyToken } = require('./auth');
const User = require('../models/User');
const Trade = require('../models/Trade');
const { AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

const router = express.Router();

// Get user profile (public)
router.get('/:userId', async (req, res, next) => {
  try {
    const { userId } = req.params;

    const user = await User.findById(userId)
      .select('-password -security.twoFactorSecret -security.loginAttempts -security.lockUntil');

    if (!user) {
      throw new AppError('User not found', 404);
    }

    // Get trade statistics
    const tradeStats = await Trade.getTradeStats(userId);

    res.json({
      success: true,
      data: {
        user: {
          id: user._id,
          username: user.username,
          profile: {
            firstName: user.profile.firstName,
            lastName: user.profile.lastName,
            avatar: user.profile.avatar,
            location: user.profile.location,
            bio: user.profile.bio
          },
          reputation: user.reputation,
          verification: {
            identity: user.verification.identity,
            phone: user.verification.phone,
            email: user.verification.email
          },
          joinedAt: user.createdAt,
          lastActive: user.lastActive
        },
        statistics: tradeStats[0] || {
          totalTrades: 0,
          totalVolume: 0,
          averageTradeSize: 0,
          currencies: []
        }
      }
    });

  } catch (error) {
    next(error);
  }
});

// Update user profile (requires auth)
router.put('/profile', verifyToken, [
  body('firstName').optional().isLength({ min: 1, max: 50 }).trim(),
  body('lastName').optional().isLength({ min: 1, max: 50 }).trim(),
  body('bio').optional().isLength({ max: 500 }).trim(),
  body('avatar').optional().isURL(),
  body('location.city').optional().isLength({ min: 1, max: 100 }).trim(),
  body('preferences.language').optional().isIn(['en', 'sw']),
  body('preferences.currency').optional().isIn(['KES', 'TZS', 'UGX', 'RWF', 'USD']),
  body('preferences.notifications.email').optional().isBoolean(),
  body('preferences.notifications.sms').optional().isBoolean(),
  body('preferences.notifications.push').optional().isBoolean()
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const updates = req.body;
    const user = req.user;

    // Update profile fields
    if (updates.firstName) user.profile.firstName = updates.firstName;
    if (updates.lastName) user.profile.lastName = updates.lastName;
    if (updates.bio !== undefined) user.profile.bio = updates.bio;
    if (updates.avatar) user.profile.avatar = updates.avatar;
    if (updates.location?.city) user.profile.location.city = updates.location.city;

    // Update preferences
    if (updates.preferences) {
      if (updates.preferences.language) user.preferences.language = updates.preferences.language;
      if (updates.preferences.currency) user.preferences.currency = updates.preferences.currency;

      if (updates.preferences.notifications) {
        Object.keys(updates.preferences.notifications).forEach(key => {
          if (typeof updates.preferences.notifications[key] === 'boolean') {
            user.preferences.notifications[key] = updates.preferences.notifications[key];
          }
        });
      }
    }

    await user.save();

    logger.info(`Profile updated for user: ${user.username}`);

    res.json({
      success: true,
      message: 'Profile updated successfully',
      data: { user }
    });

  } catch (error) {
    next(error);
  }
});

// Get user reputation details
router.get('/:userId/reputation', async (req, res, next) => {
  try {
    const { userId } = req.params;

    const user = await User.findById(userId);
    if (!user) {
      throw new AppError('User not found', 404);
    }

    // Get detailed trade statistics
    const completedTrades = await Trade.find({
      $or: [{ seller: userId }, { buyer: userId }],
      status: 'completed'
    }).populate('seller buyer', 'username');

    const disputedTrades = await Trade.find({
      $or: [{ seller: userId }, { buyer: userId }],
      status: 'disputed'
    }).populate('dispute');

    const cancelledTrades = await Trade.find({
      $or: [{ seller: userId }, { buyer: userId }],
      status: 'cancelled'
    });

    // Calculate reputation metrics
    const totalTrades = completedTrades.length;
    const totalDisputes = disputedTrades.length;
    const totalCancellations = cancelledTrades.length;

    const disputeRate = totalTrades > 0 ? (totalDisputes / totalTrades) * 100 : 0;
    const cancellationRate = totalTrades > 0 ? (totalCancellations / totalTrades) * 100 : 0;

    const totalVolume = completedTrades.reduce((sum, trade) => {
      return sum + parseFloat(trade.cryptocurrency.amount);
    }, 0);

    const averageTradeSize = totalTrades > 0 ? totalVolume / totalTrades : 0;

    // Get recent feedback (last 10 trades)
    const recentTrades = completedTrades
      .sort((a, b) => b.completedAt - a.completedAt)
      .slice(0, 10);

    res.json({
      success: true,
      data: {
        reputation: user.reputation,
        statistics: {
          totalTrades,
          totalDisputes,
          totalCancellations,
          disputeRate: Math.round(disputeRate * 100) / 100,
          cancellationRate: Math.round(cancellationRate * 100) / 100,
          totalVolume,
          averageTradeSize: Math.round(averageTradeSize * 100) / 100
        },
        recentTrades: recentTrades.map(trade => ({
          tradeId: trade.tradeId,
          amount: trade.cryptocurrency.amount,
          currency: trade.cryptocurrency.symbol,
          completedAt: trade.completedAt,
          counterparty: trade.seller._id.toString() === userId ? trade.buyer.username : trade.seller.username
        }))
      }
    });

  } catch (error) {
    next(error);
  }
});

// Search users
router.get('/', [
  query('search').optional().isLength({ min: 2, max: 50 }),
  query('country').optional().isIn(['KE', 'TZ', 'UG', 'RW']),
  query('minReputation').optional().isInt({ min: 0, max: 100 }),
  query('verified').optional().isBoolean(),
  query('limit').optional().isInt({ min: 1, max: 50 }),
  query('offset').optional().isInt({ min: 0 })
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const {
      search,
      country,
      minReputation,
      verified,
      limit = 20,
      offset = 0
    } = req.query;

    // Build query
    const query = { status: 'active' };

    if (search) {
      query.$or = [
        { username: { $regex: search, $options: 'i' } },
        { 'profile.firstName': { $regex: search, $options: 'i' } },
        { 'profile.lastName': { $regex: search, $options: 'i' } }
      ];
    }

    if (country) {
      query['profile.location.country'] = country;
    }

    if (minReputation) {
      query['reputation.score'] = { $gte: parseInt(minReputation) };
    }

    if (verified === 'true') {
      query['verification.identity.verified'] = true;
    }

    const users = await User.find(query)
      .select('username profile reputation verification createdAt lastActive')
      .sort({ 'reputation.score': -1, createdAt: -1 })
      .limit(parseInt(limit))
      .skip(parseInt(offset));

    const total = await User.countDocuments(query);

    res.json({
      success: true,
      data: {
        users,
        pagination: {
          total,
          limit: parseInt(limit),
          offset: parseInt(offset),
          hasMore: parseInt(offset) + parseInt(limit) < total
        }
      }
    });

  } catch (error) {
    next(error);
  }
});

module.exports = router;