{"name": "kryptopesa-backend", "version": "1.0.0", "description": "Backend API for KryptoPesa P2P trading platform", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "build": "echo 'No build step required for Node.js'", "test": "jest --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test:watch": "jest --watch", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "seed": "node src/scripts/seedDatabase.js"}, "dependencies": {"express": "^4.18.2", "mongoose": "^7.4.1", "redis": "^4.6.7", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.1", "joi": "^17.9.2", "cors": "^2.8.5", "helmet": "^7.0.0", "express-rate-limit": "^6.8.1", "socket.io": "^4.7.2", "ethers": "^5.7.2", "bitcoinjs-lib": "^6.1.3", "axios": "^1.4.0", "multer": "^1.4.5-lts.1", "cloudinary": "^1.38.0", "nodemailer": "^6.9.4", "winston": "^3.10.0", "dotenv": "^16.3.1", "express-validator": "^7.0.1", "compression": "^1.7.4", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.1", "supertest": "^6.3.3", "eslint": "^8.45.0", "@types/jest": "^29.5.3"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js", "!src/server.js"]}, "engines": {"node": ">=18.0.0"}}