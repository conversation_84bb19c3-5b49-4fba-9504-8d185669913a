{"name": "kryptopesa-platform", "version": "1.0.0", "description": "P2P Cryptocurrency Trading Platform for East Africa", "main": "index.js", "scripts": {"install:all": "npm install && cd backend && npm install && cd ../mobile && npm install && cd ../admin-dashboard && npm install && cd ../smart-contracts && npm install", "dev:backend": "cd backend && npm run dev", "dev:mobile": "cd mobile && npm run android", "dev:admin": "cd admin-dashboard && npm start", "dev:contracts": "cd smart-contracts && npx hardhat node", "dev:all": "concurrently \"npm run dev:backend\" \"npm run dev:contracts\"", "build:all": "cd backend && npm run build && cd ../mobile && npm run build && cd ../admin-dashboard && npm run build", "test:all": "cd backend && npm test && cd ../mobile && npm test && cd ../smart-contracts && npm test", "deploy:contracts": "cd smart-contracts && npx hardhat run scripts/deploy.js --network polygon", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "format": "prettier --write ."}, "keywords": ["cryptocurrency", "p2p", "trading", "escrow", "blockchain", "east-africa", "mobile"], "author": "KryptoPesa Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.0", "eslint": "^8.45.0", "prettier": "^3.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}