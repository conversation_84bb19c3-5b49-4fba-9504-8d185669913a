import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { offerService } from '../../services/offerService';
import { showMessage } from 'react-native-flash-message';

// Async thunks
export const getOffers = createAsyncThunk(
  'offer/getOffers',
  async (filters, { rejectWithValue }) => {
    try {
      const response = await offerService.getOffers(filters);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to get offers');
    }
  }
);

export const getMyOffers = createAsyncThunk(
  'offer/getMyOffers',
  async (_, { rejectWithValue }) => {
    try {
      const response = await offerService.getMyOffers();
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to get your offers');
    }
  }
);

export const createOffer = createAsyncThunk(
  'offer/createOffer',
  async (offerData, { rejectWithValue }) => {
    try {
      const response = await offerService.createOffer(offerData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create offer');
    }
  }
);

export const updateOffer = createAsyncThunk(
  'offer/updateOffer',
  async ({ offerId, updates }, { rejectWithValue }) => {
    try {
      const response = await offerService.updateOffer(offerId, updates);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update offer');
    }
  }
);

export const deleteOffer = createAsyncThunk(
  'offer/deleteOffer',
  async (offerId, { rejectWithValue }) => {
    try {
      await offerService.deleteOffer(offerId);
      return offerId;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete offer');
    }
  }
);

export const respondToOffer = createAsyncThunk(
  'offer/respondToOffer',
  async ({ offerId, amount, paymentMethod }, { rejectWithValue }) => {
    try {
      const response = await offerService.respondToOffer(offerId, amount, paymentMethod);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to respond to offer');
    }
  }
);

const initialState = {
  offers: [],
  myOffers: [],
  selectedOffer: null,
  pagination: {
    total: 0,
    limit: 20,
    offset: 0,
    hasMore: false,
  },
  isLoading: false,
  isLoadingMore: false,
  isCreating: false,
  isUpdating: false,
  error: null,
};

const offerSlice = createSlice({
  name: 'offer',
  initialState,
  reducers: {
    clearOffers: (state) => {
      state.offers = [];
      state.pagination = initialState.pagination;
    },
    clearMyOffers: (state) => {
      state.myOffers = [];
    },
    clearError: (state) => {
      state.error = null;
    },
    setSelectedOffer: (state, action) => {
      state.selectedOffer = action.payload;
    },
    updateOfferInList: (state, action) => {
      const { offerId, updates } = action.payload;
      const offerIndex = state.offers.findIndex(offer => offer.offerId === offerId);
      if (offerIndex >= 0) {
        state.offers[offerIndex] = { ...state.offers[offerIndex], ...updates };
      }

      const myOfferIndex = state.myOffers.findIndex(offer => offer.offerId === offerId);
      if (myOfferIndex >= 0) {
        state.myOffers[myOfferIndex] = { ...state.myOffers[myOfferIndex], ...updates };
      }
    },
    removeOfferFromList: (state, action) => {
      const offerId = action.payload;
      state.offers = state.offers.filter(offer => offer.offerId !== offerId);
      state.myOffers = state.myOffers.filter(offer => offer.offerId !== offerId);
    },
  },
  extraReducers: (builder) => {
    builder
      // Get offers
      .addCase(getOffers.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getOffers.fulfilled, (state, action) => {
        state.isLoading = false;
        state.offers = action.payload.offers;
        state.pagination = action.payload.pagination || initialState.pagination;
      })
      .addCase(getOffers.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Get my offers
      .addCase(getMyOffers.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getMyOffers.fulfilled, (state, action) => {
        state.isLoading = false;
        state.myOffers = action.payload.offers;
      })
      .addCase(getMyOffers.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Create offer
      .addCase(createOffer.pending, (state) => {
        state.isCreating = true;
        state.error = null;
      })
      .addCase(createOffer.fulfilled, (state, action) => {
        state.isCreating = false;
        state.myOffers.unshift(action.payload.offer);

        showMessage({
          message: 'Offer Created!',
          description: 'Your trading offer has been published',
          type: 'success',
        });
      })
      .addCase(createOffer.rejected, (state, action) => {
        state.isCreating = false;
        state.error = action.payload;

        showMessage({
          message: 'Failed to Create Offer',
          description: action.payload,
          type: 'danger',
        });
      })

      // Update offer
      .addCase(updateOffer.pending, (state) => {
        state.isUpdating = true;
        state.error = null;
      })
      .addCase(updateOffer.fulfilled, (state, action) => {
        state.isUpdating = false;
        const updatedOffer = action.payload.offer;

        // Update in offers list
        const offerIndex = state.offers.findIndex(offer => offer.offerId === updatedOffer.offerId);
        if (offerIndex >= 0) {
          state.offers[offerIndex] = updatedOffer;
        }

        // Update in my offers list
        const myOfferIndex = state.myOffers.findIndex(offer => offer.offerId === updatedOffer.offerId);
        if (myOfferIndex >= 0) {
          state.myOffers[myOfferIndex] = updatedOffer;
        }

        showMessage({
          message: 'Offer Updated',
          description: 'Your offer has been updated successfully',
          type: 'success',
        });
      })
      .addCase(updateOffer.rejected, (state, action) => {
        state.isUpdating = false;
        state.error = action.payload;

        showMessage({
          message: 'Update Failed',
          description: action.payload,
          type: 'danger',
        });
      })

      // Delete offer
      .addCase(deleteOffer.fulfilled, (state, action) => {
        const offerId = action.payload;
        state.offers = state.offers.filter(offer => offer.offerId !== offerId);
        state.myOffers = state.myOffers.filter(offer => offer.offerId !== offerId);

        showMessage({
          message: 'Offer Deleted',
          description: 'Your offer has been removed',
          type: 'info',
        });
      })
      .addCase(deleteOffer.rejected, (state, action) => {
        state.error = action.payload;

        showMessage({
          message: 'Delete Failed',
          description: action.payload,
          type: 'danger',
        });
      })

      // Respond to offer
      .addCase(respondToOffer.fulfilled, (state, action) => {
        showMessage({
          message: 'Trade Created!',
          description: 'Your response has created a new trade',
          type: 'success',
        });
      })
      .addCase(respondToOffer.rejected, (state, action) => {
        state.error = action.payload;

        showMessage({
          message: 'Response Failed',
          description: action.payload,
          type: 'danger',
        });
      });
  },
});

export const {
  clearOffers,
  clearMyOffers,
  clearError,
  setSelectedOffer,
  updateOfferInList,
  removeOfferFromList,
} = offerSlice.actions;

export default offerSlice.reducer;

// Selectors
export const selectOffers = (state) => state.offer.offers;
export const selectMyOffers = (state) => state.offer.myOffers;
export const selectSelectedOffer = (state) => state.offer.selectedOffer;
export const selectOfferLoading = (state) => state.offer.isLoading;
export const selectOfferCreating = (state) => state.offer.isCreating;
export const selectOfferError = (state) => state.offer.error;
