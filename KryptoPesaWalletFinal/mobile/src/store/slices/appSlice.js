import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  isFirstLaunch: true,
  hasCompletedOnboarding: false,
  biometricEnabled: false,
  notificationsEnabled: true,
  language: 'en',
  currency: 'USD',
  theme: 'light',
  networkStatus: 'online',
  appVersion: '1.0.0',
};

const appSlice = createSlice({
  name: 'app',
  initialState,
  reducers: {
    setFirstLaunch: (state, action) => {
      state.isFirstLaunch = action.payload;
    },
    setOnboardingCompleted: (state, action) => {
      state.hasCompletedOnboarding = action.payload;
    },
    setBiometricEnabled: (state, action) => {
      state.biometricEnabled = action.payload;
    },
    setNotificationsEnabled: (state, action) => {
      state.notificationsEnabled = action.payload;
    },
    setLanguage: (state, action) => {
      state.language = action.payload;
    },
    setCurrency: (state, action) => {
      state.currency = action.payload;
    },
    setTheme: (state, action) => {
      state.theme = action.payload;
    },
    setNetworkStatus: (state, action) => {
      state.networkStatus = action.payload;
    },
  },
});

export const {
  setFirstLaunch,
  setOnboardingCompleted,
  setBiometricEnabled,
  setNotificationsEnabled,
  setLanguage,
  setCurrency,
  setTheme,
  setNetworkStatus,
} = appSlice.actions;

export default appSlice.reducer;
