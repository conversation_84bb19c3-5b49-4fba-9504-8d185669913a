import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { tradeService } from '../../services/tradeService';

export const getActiveTrades = createAsyncThunk(
  'trade/getActiveTrades',
  async (_, { rejectWithValue }) => {
    try {
      const response = await tradeService.getActiveTrades();
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to get trades');
    }
  }
);

export const createTrade = createAsyncThunk(
  'trade/create',
  async (tradeData, { rejectWithValue }) => {
    try {
      const response = await tradeService.createTrade(tradeData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create trade');
    }
  }
);

const initialState = {
  activeTrades: [],
  tradeHistory: [],
  currentTrade: null,
  isLoading: false,
  error: null,
};

const tradeSlice = createSlice({
  name: 'trade',
  initialState,
  reducers: {
    clearTrades: () => initialState,
    setCurrentTrade: (state, action) => {
      state.currentTrade = action.payload;
    },
    updateTradeStatus: (state, action) => {
      const { tradeId, status, timestamp, details } = action.payload;

      // Update in active trades
      const activeTradeIndex = state.activeTrades.findIndex(trade => trade.tradeId === tradeId);
      if (activeTradeIndex >= 0) {
        state.activeTrades[activeTradeIndex].status = status;
        state.activeTrades[activeTradeIndex].lastUpdated = timestamp;
        if (details) {
          state.activeTrades[activeTradeIndex] = {
            ...state.activeTrades[activeTradeIndex],
            ...details
          };
        }
      }

      // Update current trade if it matches
      if (state.currentTrade?.tradeId === tradeId) {
        state.currentTrade.status = status;
        state.currentTrade.lastUpdated = timestamp;
        if (details) {
          state.currentTrade = { ...state.currentTrade, ...details };
        }
      }
    },
    addTradeMessage: (state, action) => {
      const { tradeId, message } = action.payload;

      // Update trade with new message info
      const activeTradeIndex = state.activeTrades.findIndex(trade => trade.tradeId === tradeId);
      if (activeTradeIndex >= 0) {
        if (!state.activeTrades[activeTradeIndex].lastMessage) {
          state.activeTrades[activeTradeIndex].lastMessage = message;
        }
        state.activeTrades[activeTradeIndex].unreadCount =
          (state.activeTrades[activeTradeIndex].unreadCount || 0) + 1;
      }
    },
    markTradeMessagesRead: (state, action) => {
      const { tradeId } = action.payload;

      const activeTradeIndex = state.activeTrades.findIndex(trade => trade.tradeId === tradeId);
      if (activeTradeIndex >= 0) {
        state.activeTrades[activeTradeIndex].unreadCount = 0;
      }
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getActiveTrades.fulfilled, (state, action) => {
        state.activeTrades = action.payload.trades;
      })
      .addCase(createTrade.fulfilled, (state, action) => {
        state.activeTrades.unshift(action.payload.trade);
      });
  },
});

export const { clearTrades, setCurrentTrade, updateTradeStatus } = tradeSlice.actions;
export default tradeSlice.reducer;
