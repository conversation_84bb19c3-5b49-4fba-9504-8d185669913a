import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { tradeService } from '../../services/tradeService';

export const getActiveTrades = createAsyncThunk(
  'trade/getActiveTrades',
  async (_, { rejectWithValue }) => {
    try {
      const response = await tradeService.getActiveTrades();
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to get trades');
    }
  }
);

export const createTrade = createAsyncThunk(
  'trade/create',
  async (tradeData, { rejectWithValue }) => {
    try {
      const response = await tradeService.createTrade(tradeData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create trade');
    }
  }
);

const initialState = {
  activeTrades: [],
  tradeHistory: [],
  currentTrade: null,
  isLoading: false,
  error: null,
};

const tradeSlice = createSlice({
  name: 'trade',
  initialState,
  reducers: {
    clearTrades: () => initialState,
    setCurrentTrade: (state, action) => {
      state.currentTrade = action.payload;
    },
    updateTradeStatus: (state, action) => {
      const { tradeId, status } = action.payload;
      const trade = state.activeTrades.find(t => t.tradeId === tradeId);
      if (trade) {
        trade.status = status;
      }
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getActiveTrades.fulfilled, (state, action) => {
        state.activeTrades = action.payload.trades;
      })
      .addCase(createTrade.fulfilled, (state, action) => {
        state.activeTrades.unshift(action.payload.trade);
      });
  },
});

export const { clearTrades, setCurrentTrade, updateTradeStatus } = tradeSlice.actions;
export default tradeSlice.reducer;
