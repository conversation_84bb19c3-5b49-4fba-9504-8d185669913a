import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { walletService } from '../../services/walletService';
import { showMessage } from 'react-native-flash-message';

// Async thunks
export const createWallet = createAsyncThunk(
  'wallet/create',
  async (_, { rejectWithValue }) => {
    try {
      const response = await walletService.createWallet();
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create wallet');
    }
  }
);

export const importWallet = createAsyncThunk(
  'wallet/import',
  async ({ mnemonic }, { rejectWithValue }) => {
    try {
      const response = await walletService.importWallet(mnemonic);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to import wallet');
    }
  }
);

export const getWallet = createAsyncThunk(
  'wallet/get',
  async (_, { rejectWithValue }) => {
    try {
      const response = await walletService.getWallet();
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to get wallet');
    }
  }
);

export const refreshBalances = createAsyncThunk(
  'wallet/refreshBalances',
  async (_, { rejectWithValue }) => {
    try {
      const response = await walletService.refreshBalances();
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to refresh balances');
    }
  }
);

export const getTransactionHistory = createAsyncThunk(
  'wallet/getTransactionHistory',
  async ({ limit = 50, offset = 0 }, { rejectWithValue }) => {
    try {
      const response = await walletService.getTransactionHistory(limit, offset);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to get transactions');
    }
  }
);

export const verifyMnemonic = createAsyncThunk(
  'wallet/verifyMnemonic',
  async ({ mnemonic }, { rejectWithValue }) => {
    try {
      const response = await walletService.verifyMnemonic(mnemonic);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to verify mnemonic');
    }
  }
);

export const markBackupCompleted = createAsyncThunk(
  'wallet/markBackupCompleted',
  async (_, { rejectWithValue }) => {
    try {
      const response = await walletService.markBackupCompleted();
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to mark backup completed');
    }
  }
);

const initialState = {
  wallet: null,
  balances: [],
  transactions: [],
  transactionHistory: {
    transactions: [],
    total: 0,
    hasMore: false,
  },
  stats: null,
  isLoading: false,
  isRefreshing: false,
  error: null,
  backupMnemonic: null, // Temporarily store mnemonic for backup
  hasWallet: false,
};

const walletSlice = createSlice({
  name: 'wallet',
  initialState,
  reducers: {
    clearWallet: (state) => {
      return initialState;
    },
    clearError: (state) => {
      state.error = null;
    },
    clearBackupMnemonic: (state) => {
      state.backupMnemonic = null;
    },
    updateBalance: (state, action) => {
      const { symbol, network, balance } = action.payload;
      const balanceIndex = state.balances.findIndex(
        b => b.symbol === symbol && b.network === network
      );
      
      if (balanceIndex >= 0) {
        state.balances[balanceIndex].balance = balance;
        state.balances[balanceIndex].lastUpdated = new Date().toISOString();
      }
    },
    addTransaction: (state, action) => {
      state.transactions.unshift(action.payload);
      // Keep only last 100 transactions in memory
      if (state.transactions.length > 100) {
        state.transactions = state.transactions.slice(0, 100);
      }
    },
    updateTransactionStatus: (state, action) => {
      const { hash, status, confirmations } = action.payload;
      const transaction = state.transactions.find(tx => tx.hash === hash);
      if (transaction) {
        transaction.status = status;
        transaction.confirmations = confirmations;
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Create wallet
      .addCase(createWallet.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createWallet.fulfilled, (state, action) => {
        state.isLoading = false;
        state.hasWallet = true;
        state.backupMnemonic = action.payload.mnemonic;
        
        showMessage({
          message: 'Wallet Created!',
          description: 'Please backup your recovery phrase',
          type: 'success',
        });
      })
      .addCase(createWallet.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
        
        showMessage({
          message: 'Wallet Creation Failed',
          description: action.payload,
          type: 'danger',
        });
      })
      
      // Import wallet
      .addCase(importWallet.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(importWallet.fulfilled, (state, action) => {
        state.isLoading = false;
        state.hasWallet = true;
        
        showMessage({
          message: 'Wallet Imported!',
          description: 'Your wallet has been successfully imported',
          type: 'success',
        });
      })
      .addCase(importWallet.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
        
        showMessage({
          message: 'Import Failed',
          description: action.payload,
          type: 'danger',
        });
      })
      
      // Get wallet
      .addCase(getWallet.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getWallet.fulfilled, (state, action) => {
        state.isLoading = false;
        state.wallet = action.payload.wallet;
        state.balances = action.payload.wallet.balances || [];
        state.transactions = action.payload.wallet.transactions || [];
        state.hasWallet = true;
      })
      .addCase(getWallet.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
        state.hasWallet = false;
      })
      
      // Refresh balances
      .addCase(refreshBalances.pending, (state) => {
        state.isRefreshing = true;
      })
      .addCase(refreshBalances.fulfilled, (state, action) => {
        state.isRefreshing = false;
        state.balances = action.payload.balances;
        
        showMessage({
          message: 'Balances Updated',
          type: 'info',
        });
      })
      .addCase(refreshBalances.rejected, (state, action) => {
        state.isRefreshing = false;
        state.error = action.payload;
      })
      
      // Get transaction history
      .addCase(getTransactionHistory.fulfilled, (state, action) => {
        const { transactions, total, hasMore } = action.payload;
        state.transactionHistory = {
          transactions: [...state.transactionHistory.transactions, ...transactions],
          total,
          hasMore,
        };
      })
      
      // Mark backup completed
      .addCase(markBackupCompleted.fulfilled, (state) => {
        if (state.wallet) {
          state.wallet.security.backupCompleted = true;
        }
        state.backupMnemonic = null;
        
        showMessage({
          message: 'Backup Completed',
          description: 'Your wallet backup is now secure',
          type: 'success',
        });
      });
  },
});

export const {
  clearWallet,
  clearError,
  clearBackupMnemonic,
  updateBalance,
  addTransaction,
  updateTransactionStatus,
} = walletSlice.actions;

export default walletSlice.reducer;

// Selectors
export const selectWallet = (state) => state.wallet.wallet;
export const selectBalances = (state) => state.wallet.balances;
export const selectTransactions = (state) => state.wallet.transactions;
export const selectWalletLoading = (state) => state.wallet.isLoading;
export const selectWalletRefreshing = (state) => state.wallet.isRefreshing;
export const selectHasWallet = (state) => state.wallet.hasWallet;
export const selectBackupMnemonic = (state) => state.wallet.backupMnemonic;
