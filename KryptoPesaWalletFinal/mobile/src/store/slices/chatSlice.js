import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  chats: {},
  activeChat: null,
  unreadCounts: {},
  isConnected: false,
};

const chatSlice = createSlice({
  name: 'chat',
  initialState,
  reducers: {
    clearChats: () => initialState,
    setConnected: (state, action) => {
      state.isConnected = action.payload;
    },
    addMessage: (state, action) => {
      const { tradeId, message } = action.payload;
      if (!state.chats[tradeId]) {
        state.chats[tradeId] = [];
      }
      state.chats[tradeId].push(message);
    },
    setActiveChat: (state, action) => {
      state.activeChat = action.payload;
    },
  },
});

export const { clearChats, setConnected, addMessage, setActiveChat } = chatSlice.actions;
export default chatSlice.reducer;
