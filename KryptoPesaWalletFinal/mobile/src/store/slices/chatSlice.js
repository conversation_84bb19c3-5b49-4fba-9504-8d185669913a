import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { chatService } from '../../services/chatService';
import { showMessage } from 'react-native-flash-message';

// Async thunks
export const getChatMessages = createAsyncThunk(
  'chat/getChatMessages',
  async ({ tradeId, limit = 50, offset = 0 }, { rejectWithValue }) => {
    try {
      const response = await chatService.getMessages(tradeId, limit, offset);
      return { tradeId, ...response.data };
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to get messages');
    }
  }
);

export const sendChatMessage = createAsyncThunk(
  'chat/sendChatMessage',
  async ({ tradeId, content, type = 'text', attachments = [] }, { rejectWithValue }) => {
    try {
      const response = await chatService.sendMessage(tradeId, content, type, attachments);
      return { tradeId, message: response.data.message };
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to send message');
    }
  }
);

export const markMessagesAsRead = createAsyncThunk(
  'chat/markMessagesAsRead',
  async ({ tradeId, messageIds }, { rejectWithValue }) => {
    try {
      await chatService.markAsRead(tradeId, messageIds);
      return { tradeId, messageIds };
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to mark messages as read');
    }
  }
);

const initialState = {
  // Chat data by tradeId
  chats: {}, // { tradeId: { messages: [], participants: [], status: '', pagination: {} } }

  // Typing indicators by tradeId
  typingUsers: {}, // { tradeId: { userId: { username: '', isTyping: true, timestamp: '' } } }

  // Current active chat
  activeTradeId: null,

  // Loading states
  isLoading: false,
  isSending: false,

  // Error state
  error: null,
};

const chatSlice = createSlice({
  name: 'chat',
  initialState,
  reducers: {
    clearChats: () => initialState,

    setActiveChat: (state, action) => {
      state.activeTradeId = action.payload;
    },

    addChatMessage: (state, action) => {
      const { tradeId, message } = action.payload;

      if (!state.chats[tradeId]) {
        state.chats[tradeId] = {
          messages: [],
          participants: [],
          status: 'active',
          pagination: { total: 0, hasMore: false }
        };
      }

      // Add message if it doesn't already exist
      const existingMessage = state.chats[tradeId].messages.find(m => m._id === message._id);
      if (!existingMessage) {
        state.chats[tradeId].messages.push(message);
        state.chats[tradeId].pagination.total += 1;
      }
    },

    updateMessageStatus: (state, action) => {
      const { tradeId, messageIds, status } = action.payload;

      if (state.chats[tradeId]) {
        state.chats[tradeId].messages.forEach(message => {
          if (messageIds.includes(message._id)) {
            if (!message.readBy) message.readBy = [];
            if (status === 'read' && !message.readBy.some(r => r.user === action.meta?.userId)) {
              message.readBy.push({
                user: action.meta?.userId,
                readAt: new Date().toISOString()
              });
            }
          }
        });
      }
    },

    setTypingStatus: (state, action) => {
      const { tradeId, userId, username, isTyping } = action.payload;

      if (!state.typingUsers[tradeId]) {
        state.typingUsers[tradeId] = {};
      }

      if (isTyping) {
        state.typingUsers[tradeId][userId] = {
          username,
          isTyping: true,
          timestamp: new Date().toISOString()
        };
      } else {
        delete state.typingUsers[tradeId][userId];
      }
    },

    clearTypingStatus: (state, action) => {
      const { tradeId } = action.payload;
      if (state.typingUsers[tradeId]) {
        state.typingUsers[tradeId] = {};
      }
    },

    updateChatStatus: (state, action) => {
      const { tradeId, status } = action.payload;
      if (state.chats[tradeId]) {
        state.chats[tradeId].status = status;
      }
    },

    clearError: (state) => {
      state.error = null;
    },
  },

  extraReducers: (builder) => {
    builder
      // Get chat messages
      .addCase(getChatMessages.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getChatMessages.fulfilled, (state, action) => {
        state.isLoading = false;
        const { tradeId, chat, messages, pagination } = action.payload;

        state.chats[tradeId] = {
          messages: messages || [],
          participants: chat?.participants || [],
          status: chat?.status || 'active',
          pagination: pagination || { total: 0, hasMore: false }
        };
      })
      .addCase(getChatMessages.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Send message
      .addCase(sendChatMessage.pending, (state) => {
        state.isSending = true;
        state.error = null;
      })
      .addCase(sendChatMessage.fulfilled, (state, action) => {
        state.isSending = false;
        const { tradeId, message } = action.payload;

        if (!state.chats[tradeId]) {
          state.chats[tradeId] = {
            messages: [],
            participants: [],
            status: 'active',
            pagination: { total: 0, hasMore: false }
          };
        }

        // Add the sent message
        state.chats[tradeId].messages.push(message);
        state.chats[tradeId].pagination.total += 1;
      })
      .addCase(sendChatMessage.rejected, (state, action) => {
        state.isSending = false;
        state.error = action.payload;

        showMessage({
          message: 'Failed to send message',
          description: action.payload,
          type: 'danger',
        });
      })

      // Mark messages as read
      .addCase(markMessagesAsRead.fulfilled, (state, action) => {
        const { tradeId, messageIds } = action.payload;

        if (state.chats[tradeId]) {
          state.chats[tradeId].messages.forEach(message => {
            if (messageIds.includes(message._id)) {
              message.isRead = true;
            }
          });
        }
      });
  },
});

export const {
  clearChats,
  setActiveChat,
  addChatMessage,
  updateMessageStatus,
  setTypingStatus,
  clearTypingStatus,
  updateChatStatus,
  clearError,
} = chatSlice.actions;

export default chatSlice.reducer;

// Selectors
export const selectChatMessages = (state, tradeId) =>
  state.chat.chats[tradeId]?.messages || [];

export const selectChatParticipants = (state, tradeId) =>
  state.chat.chats[tradeId]?.participants || [];

export const selectChatStatus = (state, tradeId) =>
  state.chat.chats[tradeId]?.status || 'active';

export const selectTypingUsers = (state, tradeId) =>
  Object.values(state.chat.typingUsers[tradeId] || {}).filter(user => user.isTyping);

export const selectChatLoading = (state) => state.chat.isLoading;
export const selectChatSending = (state) => state.chat.isSending;
export const selectChatError = (state) => state.chat.error;
export const selectActiveTradeId = (state) => state.chat.activeTradeId;
