import React, { useEffect } from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { useSelector, useDispatch } from 'react-redux';

import { selectIsAuthenticated, selectAuthLoading, getCurrentUser } from '../store/slices/authSlice';
import { selectHasWallet, getWallet } from '../store/slices/walletSlice';
import { selectApp } from '../store/slices/appSlice';

// Navigators
import AuthNavigator from './AuthNavigator';
import OnboardingNavigator from './OnboardingNavigator';
import WalletSetupNavigator from './WalletSetupNavigator';
import MainNavigator from './MainNavigator';

// Screens
import LoadingScreen from '../components/LoadingScreen';
import SplashScreen from '../screens/SplashScreen';

const Stack = createStackNavigator();

const AppNavigator = () => {
  const dispatch = useDispatch();
  const isAuthenticated = useSelector(selectIsAuthenticated);
  const authLoading = useSelector(selectAuthLoading);
  const hasWallet = useSelector(selectHasWallet);
  const app = useSelector(selectApp);

  useEffect(() => {
    // Check authentication status on app start
    if (isAuthenticated) {
      dispatch(getCurrentUser());
      dispatch(getWallet());
    }
  }, [dispatch, isAuthenticated]);

  // Show loading screen while checking auth
  if (authLoading) {
    return <LoadingScreen />;
  }

  // Show splash screen on first launch
  if (app.isFirstLaunch) {
    return (
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        <Stack.Screen name="Splash" component={SplashScreen} />
      </Stack.Navigator>
    );
  }

  // Show onboarding if not completed
  if (!app.hasCompletedOnboarding) {
    return <OnboardingNavigator />;
  }

  // Show auth screens if not authenticated
  if (!isAuthenticated) {
    return <AuthNavigator />;
  }

  // Show wallet setup if authenticated but no wallet
  if (!hasWallet) {
    return <WalletSetupNavigator />;
  }

  // Show main app
  return <MainNavigator />;
};

export default AppNavigator;
