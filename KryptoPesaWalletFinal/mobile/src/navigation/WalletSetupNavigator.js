import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';

import WalletSetupScreen from '../screens/wallet/WalletSetupScreen';
import CreateWalletScreen from '../screens/wallet/CreateWalletScreen';
import ImportWalletScreen from '../screens/wallet/ImportWalletScreen';
import BackupWalletScreen from '../screens/wallet/BackupWalletScreen';

const Stack = createStackNavigator();

const WalletSetupNavigator = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="WalletSetup" component={WalletSetupScreen} />
      <Stack.Screen name="CreateWallet" component={CreateWalletScreen} />
      <Stack.Screen name="ImportWallet" component={ImportWalletScreen} />
      <Stack.Screen name="BackupWallet" component={BackupWalletScreen} />
    </Stack.Navigator>
  );
};

export default WalletSetupNavigator;
