import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import { theme } from '../utils/theme';

// Screens
import HomeScreen from '../screens/HomeScreen';
import WalletScreen from '../screens/WalletScreen';
import TradeScreen from '../screens/TradeScreen';
import OffersScreen from '../screens/OffersScreen';
import ProfileScreen from '../screens/ProfileScreen';

// Trade flow screens
import CreateOfferScreen from '../screens/CreateOfferScreen';
import TradeDetailsScreen from '../screens/TradeDetailsScreen';
import ChatScreen from '../screens/ChatScreen';

// Wallet screens
import SendScreen from '../screens/SendScreen';
import ReceiveScreen from '../screens/ReceiveScreen';
import TransactionHistoryScreen from '../screens/TransactionHistoryScreen';

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

// Home Stack
const HomeStack = () => (
  <Stack.Navigator>
    <Stack.Screen 
      name="HomeMain" 
      component={HomeScreen}
      options={{ headerShown: false }}
    />
    <Stack.Screen 
      name="TradeDetails" 
      component={TradeDetailsScreen}
      options={{ title: 'Trade Details' }}
    />
    <Stack.Screen 
      name="Chat" 
      component={ChatScreen}
      options={({ route }) => ({ 
        title: `Trade #${route.params?.tradeId || ''}` 
      })}
    />
  </Stack.Navigator>
);

// Wallet Stack
const WalletStack = () => (
  <Stack.Navigator>
    <Stack.Screen 
      name="WalletMain" 
      component={WalletScreen}
      options={{ headerShown: false }}
    />
    <Stack.Screen 
      name="Send" 
      component={SendScreen}
      options={{ title: 'Send Crypto' }}
    />
    <Stack.Screen 
      name="Receive" 
      component={ReceiveScreen}
      options={{ title: 'Receive Crypto' }}
    />
    <Stack.Screen 
      name="TransactionHistory" 
      component={TransactionHistoryScreen}
      options={{ title: 'Transaction History' }}
    />
  </Stack.Navigator>
);

// Trade Stack
const TradeStack = () => (
  <Stack.Navigator>
    <Stack.Screen 
      name="TradeMain" 
      component={TradeScreen}
      options={{ headerShown: false }}
    />
    <Stack.Screen 
      name="TradeDetails" 
      component={TradeDetailsScreen}
      options={{ title: 'Trade Details' }}
    />
    <Stack.Screen 
      name="Chat" 
      component={ChatScreen}
      options={({ route }) => ({ 
        title: `Trade #${route.params?.tradeId || ''}` 
      })}
    />
  </Stack.Navigator>
);

// Offers Stack
const OffersStack = () => (
  <Stack.Navigator>
    <Stack.Screen 
      name="OffersMain" 
      component={OffersScreen}
      options={{ headerShown: false }}
    />
    <Stack.Screen 
      name="CreateOffer" 
      component={CreateOfferScreen}
      options={{ title: 'Create Offer' }}
    />
    <Stack.Screen 
      name="TradeDetails" 
      component={TradeDetailsScreen}
      options={{ title: 'Trade Details' }}
    />
  </Stack.Navigator>
);

// Profile Stack
const ProfileStack = () => (
  <Stack.Navigator>
    <Stack.Screen 
      name="ProfileMain" 
      component={ProfileScreen}
      options={{ headerShown: false }}
    />
  </Stack.Navigator>
);

const MainNavigator = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName;

          switch (route.name) {
            case 'Home':
              iconName = focused ? 'home' : 'home-outline';
              break;
            case 'Wallet':
              iconName = focused ? 'wallet' : 'wallet-outline';
              break;
            case 'Trade':
              iconName = focused ? 'swap-horizontal' : 'swap-horizontal-variant';
              break;
            case 'Offers':
              iconName = focused ? 'format-list-bulleted' : 'format-list-bulleted-square';
              break;
            case 'Profile':
              iconName = focused ? 'account' : 'account-outline';
              break;
            default:
              iconName = 'circle';
          }

          return <Icon name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: theme.colors.primary,
        tabBarInactiveTintColor: theme.colors.onSurfaceVariant,
        tabBarStyle: {
          backgroundColor: theme.colors.surface,
          borderTopColor: theme.colors.outline,
          paddingBottom: 5,
          paddingTop: 5,
          height: 60,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '500',
        },
        headerShown: false,
      })}
    >
      <Tab.Screen 
        name="Home" 
        component={HomeStack}
        options={{ title: 'Home' }}
      />
      <Tab.Screen 
        name="Wallet" 
        component={WalletStack}
        options={{ title: 'Wallet' }}
      />
      <Tab.Screen 
        name="Trade" 
        component={TradeStack}
        options={{ title: 'Trades' }}
      />
      <Tab.Screen 
        name="Offers" 
        component={OffersStack}
        options={{ title: 'Offers' }}
      />
      <Tab.Screen 
        name="Profile" 
        component={ProfileStack}
        options={{ title: 'Profile' }}
      />
    </Tab.Navigator>
  );
};

export default MainNavigator;
