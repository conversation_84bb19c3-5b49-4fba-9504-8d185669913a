import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Text } from 'react-native-paper';
import { theme } from '../../utils/theme'; const WalletSetupScreen = () => { return ( <View style={styles.container}> <Text style={styles.title}>WalletSetupScreen</Text> <Text style={styles.subtitle}>Coming soon...</Text> </View> ); }; const styles = StyleSheet.create({ container: { flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: theme.colors.background, }, title: { fontSize: 24, fontWeight: 'bold', marginBottom: theme.spacing.md, }, subtitle: { fontSize: 16, color: theme.colors.onSurfaceVariant, }, }); export default WalletSetupScreen;
