import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Share,
  Alert,
  Clipboard,
} from 'react-native';
import {
  Text,
  Card,
  Button,
  Chip,
  Divider,
  TextInput,
  HelperText,
} from 'react-native-paper';
import { useSelector } from 'react-redux';
import QRCode from 'react-native-qrcode-svg';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import { theme } from '../utils/theme';
import { selectWallet, selectBalances } from '../store/slices/walletSlice';
import LoadingScreen from '../components/LoadingScreen';

const ReceiveScreen = ({ navigation, route }) => {
  const wallet = useSelector(selectWallet);
  const balances = useSelector(selectBalances);

  const [selectedNetwork, setSelectedNetwork] = useState(route.params?.network || 'ethereum');
  const [requestAmount, setRequestAmount] = useState('');
  const [requestNote, setRequestNote] = useState('');
  const [qrValue, setQrValue] = useState('');

  useEffect(() => {
    updateQRCode();
  }, [selectedNetwork, requestAmount, requestNote, wallet]);

  const getWalletAddress = (network) => {
    if (!wallet?.addresses) return '';

    switch (network) {
      case 'ethereum':
        return wallet.addresses.ethereum || '';
      case 'bitcoin':
        return wallet.addresses.bitcoin || '';
      default:
        return wallet.addresses.ethereum || '';
    }
  };

  const updateQRCode = () => {
    const address = getWalletAddress(selectedNetwork);
    if (!address) return;

    let qrData = address;

    // Add amount and note if specified
    if (requestAmount || requestNote) {
      const params = new URLSearchParams();
      if (requestAmount) params.append('amount', requestAmount);
      if (requestNote) params.append('message', requestNote);

      if (selectedNetwork === 'ethereum') {
        qrData = `ethereum:${address}?${params.toString()}`;
      } else if (selectedNetwork === 'bitcoin') {
        qrData = `bitcoin:${address}?${params.toString()}`;
      }
    }

    setQrValue(qrData);
  };

  const handleCopyAddress = async () => {
    const address = getWalletAddress(selectedNetwork);
    if (address) {
      await Clipboard.setString(address);
      Alert.alert('Copied!', 'Address copied to clipboard');
    }
  };

  const handleShareAddress = async () => {
    const address = getWalletAddress(selectedNetwork);
    if (address) {
      try {
        await Share.share({
          message: `My ${selectedNetwork} wallet address: ${address}`,
          title: 'Wallet Address',
        });
      } catch (error) {
        console.log('Share error:', error);
      }
    }
  };

  const handleShareQR = async () => {
    try {
      await Share.share({
        message: `Send crypto to my wallet: ${qrValue}`,
        title: 'Receive Crypto',
      });
    } catch (error) {
      console.log('Share QR error:', error);
    }
  };

  const networks = [
    { value: 'ethereum', label: 'Ethereum', tokens: ['ETH', 'USDT', 'USDC', 'DAI'] },
    { value: 'bitcoin', label: 'Bitcoin', tokens: ['BTC'] },
  ];

  const selectedNetworkInfo = networks.find(n => n.value === selectedNetwork);
  const currentAddress = getWalletAddress(selectedNetwork);

  if (!wallet) {
    return <LoadingScreen message="Loading wallet..." />;
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.title}>Receive Cryptocurrency</Text>

          {/* Network Selection */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Select Network</Text>
            <View style={styles.networkContainer}>
              {networks.map((network) => (
                <Chip
                  key={network.value}
                  selected={selectedNetwork === network.value}
                  onPress={() => setSelectedNetwork(network.value)}
                  style={styles.networkChip}
                >
                  {network.label}
                </Chip>
              ))}
            </View>

            {selectedNetworkInfo && (
              <View style={styles.supportedTokens}>
                <Text style={styles.supportedLabel}>Supported tokens:</Text>
                <Text style={styles.supportedList}>
                  {selectedNetworkInfo.tokens.join(', ')}
                </Text>
              </View>
            )}
          </View>

          <Divider style={styles.divider} />

          {/* QR Code */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>QR Code</Text>
            <View style={styles.qrContainer}>
              {qrValue ? (
                <QRCode
                  value={qrValue}
                  size={200}
                  backgroundColor="white"
                  color="black"
                />
              ) : (
                <View style={styles.qrPlaceholder}>
                  <Icon name="qrcode" size={100} color={theme.colors.onSurfaceVariant} />
                  <Text style={styles.qrPlaceholderText}>QR Code will appear here</Text>
                </View>
              )}
            </View>

            <Button
              mode="outlined"
              onPress={handleShareQR}
              style={styles.shareQRButton}
              icon="share"
            >
              Share QR Code
            </Button>
          </View>

          <Divider style={styles.divider} />

          {/* Wallet Address */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Wallet Address</Text>
            <View style={styles.addressContainer}>
              <Text style={styles.addressText} selectable>
                {currentAddress || 'No address available'}
              </Text>
            </View>

            <View style={styles.addressActions}>
              <Button
                mode="outlined"
                onPress={handleCopyAddress}
                style={styles.addressButton}
                icon="content-copy"
              >
                Copy
              </Button>
              <Button
                mode="outlined"
                onPress={handleShareAddress}
                style={styles.addressButton}
                icon="share"
              >
                Share
              </Button>
            </View>
          </View>

          <Divider style={styles.divider} />

          {/* Payment Request (Optional) */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Payment Request (Optional)</Text>
            <Text style={styles.sectionHelper}>
              Add amount and note to create a payment request QR code
            </Text>

            <TextInput
              label="Requested Amount"
              value={requestAmount}
              onChangeText={setRequestAmount}
              keyboardType="numeric"
              mode="outlined"
              style={styles.input}
              placeholder="0.00"
            />

            <TextInput
              label="Note/Message"
              value={requestNote}
              onChangeText={setRequestNote}
              mode="outlined"
              style={styles.input}
              multiline
              numberOfLines={2}
              placeholder="Payment for..."
            />

            {(requestAmount || requestNote) && (
              <HelperText type="info">
                QR code updated with payment request details
              </HelperText>
            )}
          </View>

          {/* Important Notice */}
          <View style={styles.noticeContainer}>
            <Icon name="information" size={20} color={theme.colors.warning} />
            <View style={styles.noticeText}>
              <Text style={styles.noticeTitle}>Important:</Text>
              <Text style={styles.noticeDescription}>
                Only send {selectedNetworkInfo?.tokens.join(', ')} tokens to this {selectedNetworkInfo?.label} address.
                Sending other tokens may result in permanent loss.
              </Text>
            </View>
          </View>
        </Card.Content>
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  card: {
    margin: theme.spacing.md,
    elevation: 2,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: theme.spacing.lg,
    color: theme.colors.onSurface,
  },
  section: {
    marginBottom: theme.spacing.md,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: theme.spacing.sm,
    color: theme.colors.onSurface,
  },
  sectionHelper: {
    fontSize: 12,
    color: theme.colors.onSurfaceVariant,
    marginBottom: theme.spacing.sm,
  },
  networkContainer: {
    flexDirection: 'row',
    gap: theme.spacing.sm,
    marginBottom: theme.spacing.sm,
  },
  networkChip: {
    marginBottom: theme.spacing.xs,
  },
  supportedTokens: {
    padding: theme.spacing.sm,
    backgroundColor: theme.colors.surfaceVariant,
    borderRadius: theme.roundness,
  },
  supportedLabel: {
    fontSize: 12,
    color: theme.colors.onSurfaceVariant,
    marginBottom: theme.spacing.xs,
  },
  supportedList: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.onSurface,
  },
  divider: {
    marginVertical: theme.spacing.md,
  },
  qrContainer: {
    alignItems: 'center',
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.surfaceVariant,
    borderRadius: theme.roundness,
    marginBottom: theme.spacing.md,
  },
  qrPlaceholder: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 200,
    height: 200,
  },
  qrPlaceholderText: {
    fontSize: 12,
    color: theme.colors.onSurfaceVariant,
    marginTop: theme.spacing.sm,
    textAlign: 'center',
  },
  shareQRButton: {
    alignSelf: 'center',
  },
  addressContainer: {
    padding: theme.spacing.md,
    backgroundColor: theme.colors.surfaceVariant,
    borderRadius: theme.roundness,
    marginBottom: theme.spacing.md,
  },
  addressText: {
    fontSize: 14,
    fontFamily: 'monospace',
    color: theme.colors.onSurface,
    textAlign: 'center',
    lineHeight: 20,
  },
  addressActions: {
    flexDirection: 'row',
    gap: theme.spacing.sm,
  },
  addressButton: {
    flex: 1,
  },
  input: {
    marginBottom: theme.spacing.sm,
  },
  noticeContainer: {
    flexDirection: 'row',
    padding: theme.spacing.md,
    backgroundColor: theme.colors.warningContainer,
    borderRadius: theme.roundness,
    marginTop: theme.spacing.md,
  },
  noticeText: {
    flex: 1,
    marginLeft: theme.spacing.sm,
  },
  noticeTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.onWarningContainer,
    marginBottom: theme.spacing.xs,
  },
  noticeDescription: {
    fontSize: 12,
    color: theme.colors.onWarningContainer,
    lineHeight: 16,
  },
});

export default ReceiveScreen;
