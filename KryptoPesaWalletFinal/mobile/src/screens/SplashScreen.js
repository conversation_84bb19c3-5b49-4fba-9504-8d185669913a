import React, { useEffect } from 'react';
import { View, StyleSheet, Image } from 'react-native';
import { Text } from 'react-native-paper';
import { useDispatch } from 'react-redux';
import { setFirstLaunch } from '../store/slices/appSlice';
import { theme } from '../utils/theme';

const SplashScreen = () => {
  const dispatch = useDispatch();

  useEffect(() => {
    const timer = setTimeout(() => {
      dispatch(setFirstLaunch(false));
    }, 2000);

    return () => clearTimeout(timer);
  }, [dispatch]);

  return (
    <View style={styles.container}>
      <View style={styles.logoContainer}>
        <Text style={styles.logo}>KryptoPesa</Text>
        <Text style={styles.tagline}>P2P Crypto Trading for East Africa</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.primary,
  },
  logoContainer: {
    alignItems: 'center',
  },
  logo: {
    fontSize: 32,
    fontWeight: 'bold',
    color: theme.colors.onPrimary,
    marginBottom: theme.spacing.sm,
  },
  tagline: {
    fontSize: 16,
    color: theme.colors.onPrimary,
    opacity: 0.8,
    textAlign: 'center',
  },
});

export default SplashScreen;
