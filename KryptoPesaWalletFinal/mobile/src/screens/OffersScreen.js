import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  RefreshControl,
  TouchableOpacity,
  Alert,
} from 'react-native';
import {
  Text,
  Card,
  Button,
  Chip,
  FAB,
  Searchbar,
  Menu,
  Divider,
  Avatar,
  IconButton,
} from 'react-native-paper';
import { useSelector, useDispatch } from 'react-redux';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import { theme } from '../utils/theme';
import { getOffers } from '../store/slices/offerSlice';
import { selectUser } from '../store/slices/authSlice';
import FilterModal from '../components/FilterModal';
import LoadingScreen from '../components/LoadingScreen';

const OffersScreen = ({ navigation }) => {
  const dispatch = useDispatch();
  const user = useSelector(selectUser);
  const { offers, isLoading } = useSelector(state => state.offer);

  const [searchQuery, setSearchQuery] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [filterVisible, setFilterVisible] = useState(false);
  const [sortMenuVisible, setSortMenuVisible] = useState(false);
  const [filters, setFilters] = useState({
    type: 'all', // 'all', 'buy', 'sell'
    cryptocurrency: 'all',
    fiatCurrency: user?.profile?.location?.country === 'KE' ? 'KES' : 'TZS',
    paymentMethod: 'all',
    minAmount: '',
    maxAmount: '',
  });
  const [sortBy, setSortBy] = useState('created');

  useEffect(() => {
    loadOffers();
  }, [filters, sortBy]);

  const loadOffers = useCallback(() => {
    const queryParams = {
      ...filters,
      sortBy,
      sortOrder: 'desc',
      limit: 20,
    };

    // Remove 'all' values
    Object.keys(queryParams).forEach(key => {
      if (queryParams[key] === 'all' || queryParams[key] === '') {
        delete queryParams[key];
      }
    });

    dispatch(getOffers(queryParams));
  }, [dispatch, filters, sortBy]);

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    loadOffers();
    setTimeout(() => setRefreshing(false), 1000);
  }, [loadOffers]);

  const handleOfferPress = (offer) => {
    navigation.navigate('OfferDetails', { offer });
  };

  const handleCreateOffer = () => {
    navigation.navigate('CreateOffer');
  };

  const handleRespondToOffer = (offer) => {
    if (!user) {
      Alert.alert('Authentication Required', 'Please log in to respond to offers');
      return;
    }

    if (offer.creator.id === user.id) {
      Alert.alert('Invalid Action', 'You cannot respond to your own offer');
      return;
    }

    navigation.navigate('RespondToOffer', { offer });
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return theme.colors.success;
      case 'paused': return theme.colors.warning;
      case 'inactive': return theme.colors.error;
      default: return theme.colors.onSurfaceVariant;
    }
  };

  const getCryptoIcon = (symbol) => {
    const icons = {
      'BTC': 'bitcoin',
      'ETH': 'ethereum',
      'USDT': 'currency-usd',
      'USDC': 'currency-usd',
      'DAI': 'currency-usd',
    };
    return icons[symbol] || 'currency-usd';
  };

  const formatAmount = (amount, decimals = 2) => {
    const num = parseFloat(amount);
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toFixed(decimals);
  };

  if (isLoading && offers.length === 0) {
    return <LoadingScreen message="Loading offers..." />;
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Trading Offers</Text>
        <View style={styles.headerActions}>
          <IconButton
            icon="filter-variant"
            size={24}
            onPress={() => setFilterVisible(true)}
          />
          <Menu
            visible={sortMenuVisible}
            onDismiss={() => setSortMenuVisible(false)}
            anchor={
              <IconButton
                icon="sort"
                size={24}
                onPress={() => setSortMenuVisible(true)}
              />
            }
          >
            <Menu.Item onPress={() => { setSortBy('created'); setSortMenuVisible(false); }} title="Newest" />
            <Menu.Item onPress={() => { setSortBy('price'); setSortMenuVisible(false); }} title="Best Price" />
            <Menu.Item onPress={() => { setSortBy('reputation'); setSortMenuVisible(false); }} title="Best Reputation" />
            <Menu.Item onPress={() => { setSortBy('volume'); setSortMenuVisible(false); }} title="Highest Volume" />
          </Menu>
        </View>
      </View>

      {/* Search Bar */}
      <Searchbar
        placeholder="Search offers..."
        onChangeText={setSearchQuery}
        value={searchQuery}
        style={styles.searchBar}
      />

      {/* Filter Chips */}
      <View style={styles.filterChips}>
        <Chip
          selected={filters.type !== 'all'}
          onPress={() => setFilterVisible(true)}
          style={styles.filterChip}
        >
          {filters.type === 'all' ? 'All Types' : filters.type.toUpperCase()}
        </Chip>
        <Chip
          selected={filters.cryptocurrency !== 'all'}
          onPress={() => setFilterVisible(true)}
          style={styles.filterChip}
        >
          {filters.cryptocurrency === 'all' ? 'All Crypto' : filters.cryptocurrency}
        </Chip>
        <Chip
          selected={filters.fiatCurrency !== 'all'}
          onPress={() => setFilterVisible(true)}
          style={styles.filterChip}
        >
          {filters.fiatCurrency}
        </Chip>
      </View>

      {/* Offers List */}
      <FlatList
        data={offers}
        keyExtractor={(item) => item.offerId}
        renderItem={({ item }) => <OfferCard offer={item} onPress={handleOfferPress} onRespond={handleRespondToOffer} />}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
      />

      {/* Create Offer FAB */}
      <FAB
        style={styles.fab}
        icon="plus"
        onPress={handleCreateOffer}
        label="Create Offer"
      />

      {/* Filter Modal */}
      <FilterModal
        visible={filterVisible}
        onDismiss={() => setFilterVisible(false)}
        filters={filters}
        onFiltersChange={setFilters}
      />
    </View>
  );
};

// Offer Card Component
const OfferCard = ({ offer, onPress, onRespond }) => {
  const getCryptoIcon = (symbol) => {
    const icons = {
      'BTC': 'bitcoin',
      'ETH': 'ethereum',
      'USDT': 'currency-usd',
      'USDC': 'currency-usd',
      'DAI': 'currency-usd',
    };
    return icons[symbol] || 'currency-usd';
  };

  const formatAmount = (amount, decimals = 2) => {
    const num = parseFloat(amount);
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toFixed(decimals);
  };

  const getPaymentMethodIcon = (method) => {
    const icons = {
      'mobile_money': 'cellphone',
      'bank_transfer': 'bank',
      'cash': 'cash',
      'other': 'dots-horizontal',
    };
    return icons[method] || 'dots-horizontal';
  };

  return (
    <Card style={styles.offerCard} onPress={() => onPress(offer)}>
      <Card.Content>
        {/* Header */}
        <View style={styles.offerHeader}>
          <View style={styles.offerType}>
            <Chip
              mode="outlined"
              style={[
                styles.typeChip,
                { backgroundColor: offer.type === 'buy' ? theme.colors.buy : theme.colors.sell }
              ]}
              textStyle={{ color: 'white', fontWeight: 'bold' }}
            >
              {offer.type.toUpperCase()}
            </Chip>
            <View style={styles.cryptoInfo}>
              <Icon name={getCryptoIcon(offer.cryptocurrency.symbol)} size={20} color={theme.colors.primary} />
              <Text style={styles.cryptoSymbol}>{offer.cryptocurrency.symbol}</Text>
            </View>
          </View>
          <View style={styles.priceInfo}>
            <Text style={styles.price}>
              {offer.fiat.effectivePrice.toFixed(2)} {offer.fiat.currency}
            </Text>
            <Text style={styles.priceLabel}>per {offer.cryptocurrency.symbol}</Text>
          </View>
        </View>

        {/* Amount Range */}
        <View style={styles.amountRange}>
          <Text style={styles.amountLabel}>Amount Range:</Text>
          <Text style={styles.amountValue}>
            {formatAmount(offer.cryptocurrency.minAmount)} - {formatAmount(offer.cryptocurrency.maxAmount)} {offer.cryptocurrency.symbol}
          </Text>
        </View>

        {/* Available Amount */}
        <View style={styles.availableAmount}>
          <Text style={styles.availableLabel}>Available:</Text>
          <Text style={styles.availableValue}>
            {formatAmount(offer.cryptocurrency.availableAmount)} {offer.cryptocurrency.symbol}
          </Text>
        </View>

        {/* Payment Methods */}
        <View style={styles.paymentMethods}>
          <Text style={styles.paymentLabel}>Payment:</Text>
          <View style={styles.paymentIcons}>
            {offer.paymentMethods.slice(0, 3).map((method, index) => (
              <Chip
                key={index}
                mode="outlined"
                compact
                style={styles.paymentChip}
                icon={getPaymentMethodIcon(method.method)}
              >
                {method.method.replace('_', ' ')}
              </Chip>
            ))}
            {offer.paymentMethods.length > 3 && (
              <Text style={styles.morePayments}>+{offer.paymentMethods.length - 3} more</Text>
            )}
          </View>
        </View>

        {/* Trader Info */}
        <View style={styles.traderInfo}>
          <Avatar.Text
            size={32}
            label={offer.creator.username.charAt(0).toUpperCase()}
            style={styles.avatar}
          />
          <View style={styles.traderDetails}>
            <Text style={styles.traderName}>{offer.creator.username}</Text>
            <View style={styles.reputation}>
              <Icon name="star" size={14} color={theme.colors.warning} />
              <Text style={styles.reputationScore}>
                {offer.creator.reputation.score}% ({offer.creator.reputation.completedTrades} trades)
              </Text>
            </View>
          </View>
          <View style={styles.traderBadges}>
            {offer.creator.verification.identity.verified && (
              <Icon name="check-decagram" size={16} color={theme.colors.success} />
            )}
            {offer.creator.verification.phone.verified && (
              <Icon name="phone-check" size={16} color={theme.colors.info} />
            )}
          </View>
        </View>

        {/* Action Button */}
        <Button
          mode="contained"
          onPress={() => onRespond(offer)}
          style={styles.respondButton}
          contentStyle={styles.respondButtonContent}
        >
          {offer.type === 'sell' ? 'Buy Now' : 'Sell Now'}
        </Button>
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    backgroundColor: theme.colors.surface,
    elevation: 2,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.onSurface,
  },
  headerActions: {
    flexDirection: 'row',
  },
  searchBar: {
    margin: theme.spacing.md,
    elevation: 0,
    backgroundColor: theme.colors.surfaceVariant,
  },
  filterChips: {
    flexDirection: 'row',
    paddingHorizontal: theme.spacing.md,
    paddingBottom: theme.spacing.sm,
    gap: theme.spacing.sm,
  },
  filterChip: {
    marginRight: theme.spacing.xs,
  },
  listContainer: {
    padding: theme.spacing.md,
    paddingBottom: 100, // Space for FAB
  },
  fab: {
    position: 'absolute',
    margin: theme.spacing.md,
    right: 0,
    bottom: 0,
    backgroundColor: theme.colors.primary,
  },
  // Offer Card Styles
  offerCard: {
    marginBottom: theme.spacing.md,
    elevation: 2,
  },
  offerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: theme.spacing.sm,
  },
  offerType: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.sm,
  },
  typeChip: {
    height: 28,
  },
  cryptoInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.xs,
  },
  cryptoSymbol: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.onSurface,
  },
  priceInfo: {
    alignItems: 'flex-end',
  },
  price: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  priceLabel: {
    fontSize: 12,
    color: theme.colors.onSurfaceVariant,
  },
  amountRange: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: theme.spacing.xs,
  },
  amountLabel: {
    fontSize: 14,
    color: theme.colors.onSurfaceVariant,
  },
  amountValue: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.onSurface,
  },
  availableAmount: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: theme.spacing.sm,
  },
  availableLabel: {
    fontSize: 14,
    color: theme.colors.onSurfaceVariant,
  },
  availableValue: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.success,
  },
  paymentMethods: {
    marginBottom: theme.spacing.sm,
  },
  paymentLabel: {
    fontSize: 14,
    color: theme.colors.onSurfaceVariant,
    marginBottom: theme.spacing.xs,
  },
  paymentIcons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: theme.spacing.xs,
  },
  paymentChip: {
    height: 24,
  },
  morePayments: {
    fontSize: 12,
    color: theme.colors.onSurfaceVariant,
    alignSelf: 'center',
  },
  traderInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  avatar: {
    backgroundColor: theme.colors.primary,
  },
  traderDetails: {
    flex: 1,
    marginLeft: theme.spacing.sm,
  },
  traderName: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.onSurface,
  },
  reputation: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.xs,
  },
  reputationScore: {
    fontSize: 12,
    color: theme.colors.onSurfaceVariant,
  },
  traderBadges: {
    flexDirection: 'row',
    gap: theme.spacing.xs,
  },
  respondButton: {
    marginTop: theme.spacing.xs,
  },
  respondButtonContent: {
    height: 40,
  },
});

export default OffersScreen;
