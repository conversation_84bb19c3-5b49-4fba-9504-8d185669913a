import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import {
  Text,
  TextInput,
  Button,
  Card,
  Chip,
  Divider,
  HelperText,
  IconButton,
} from 'react-native-paper';
import { useSelector, useDispatch } from 'react-redux';
import { useForm, Controller } from 'react-hook-form';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import { theme } from '../utils/theme';
import { selectBalances, selectWalletLoading } from '../store/slices/walletSlice';
import { sendTransaction } from '../store/slices/walletSlice';
import LoadingScreen from '../components/LoadingScreen';

const SendScreen = ({ navigation, route }) => {
  const dispatch = useDispatch();
  const balances = useSelector(selectBalances);
  const isLoading = useSelector(selectWalletLoading);

  const [selectedToken, setSelectedToken] = useState(route.params?.token || 'USDT');
  const [isSending, setIsSending] = useState(false);

  const { control, handleSubmit, watch, setValue, formState: { errors } } = useForm({
    defaultValues: {
      recipient: route.params?.recipient || '',
      amount: '',
      note: '',
    }
  });

  const watchAmount = watch('amount');

  useEffect(() => {
    if (route.params?.recipient) {
      setValue('recipient', route.params.recipient);
    }
  }, [route.params?.recipient, setValue]);

  const getTokenBalance = (symbol) => {
    const balance = balances.find(b => b.symbol === symbol);
    if (!balance) return '0';

    const amount = parseFloat(balance.balance) / Math.pow(10, balance.decimals);
    return amount.toFixed(6);
  };

  const getTokenBalanceRaw = (symbol) => {
    const balance = balances.find(b => b.symbol === symbol);
    return balance ? balance.balance : '0';
  };

  const validateAmount = (amount) => {
    if (!amount || amount === '') return 'Amount is required';

    const numAmount = parseFloat(amount);
    if (isNaN(numAmount) || numAmount <= 0) return 'Amount must be greater than 0';

    const availableBalance = parseFloat(getTokenBalance(selectedToken));
    if (numAmount > availableBalance) return 'Insufficient balance';

    return true;
  };

  const validateAddress = (address) => {
    if (!address || address === '') return 'Recipient address is required';

    // Basic Ethereum address validation
    if (selectedToken !== 'BTC') {
      if (!/^0x[a-fA-F0-9]{40}$/.test(address)) {
        return 'Invalid Ethereum address format';
      }
    } else {
      // Basic Bitcoin address validation (simplified)
      if (address.length < 26 || address.length > 35) {
        return 'Invalid Bitcoin address format';
      }
    }

    return true;
  };

  const onSubmit = async (data) => {
    try {
      setIsSending(true);

      const transactionData = {
        token: selectedToken,
        recipient: data.recipient.trim(),
        amount: data.amount,
        note: data.note,
      };

      const result = await dispatch(sendTransaction(transactionData));

      if (sendTransaction.fulfilled.match(result)) {
        Alert.alert(
          'Transaction Sent!',
          `Successfully sent ${data.amount} ${selectedToken} to ${data.recipient.slice(0, 10)}...`,
          [
            {
              text: 'OK',
              onPress: () => navigation.goBack(),
            },
          ]
        );
      }
    } catch (error) {
      Alert.alert('Transaction Failed', 'Please try again or check your network connection.');
    } finally {
      setIsSending(false);
    }
  };

  const handleScanQR = () => {
    navigation.navigate('QRScanner', {
      onScan: (address) => {
        setValue('recipient', address);
        navigation.goBack();
      }
    });
  };

  const handleMaxAmount = () => {
    const maxBalance = getTokenBalance(selectedToken);
    setValue('amount', maxBalance);
  };

  const availableTokens = balances.filter(balance => parseFloat(balance.balance) > 0);

  if (isLoading && balances.length === 0) {
    return <LoadingScreen message="Loading wallet..." />;
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <Card style={styles.formCard}>
          <Card.Content>
            <Text style={styles.title}>Send Cryptocurrency</Text>

            {/* Token Selection */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Select Token</Text>
              <View style={styles.tokenContainer}>
                {availableTokens.map((balance) => (
                  <Chip
                    key={balance.symbol}
                    selected={selectedToken === balance.symbol}
                    onPress={() => setSelectedToken(balance.symbol)}
                    style={styles.tokenChip}
                  >
                    {balance.symbol}
                  </Chip>
                ))}
              </View>

              {selectedToken && (
                <View style={styles.balanceInfo}>
                  <Text style={styles.balanceLabel}>Available Balance:</Text>
                  <Text style={styles.balanceAmount}>
                    {getTokenBalance(selectedToken)} {selectedToken}
                  </Text>
                </View>
              )}
            </View>

            <Divider style={styles.divider} />

            {/* Recipient Address */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Recipient Address</Text>
              <Controller
                control={control}
                name="recipient"
                rules={{ validate: validateAddress }}
                render={({ field: { onChange, value } }) => (
                  <TextInput
                    label="Wallet Address"
                    value={value}
                    onChangeText={onChange}
                    mode="outlined"
                    style={styles.input}
                    error={!!errors.recipient}
                    right={
                      <TextInput.Icon
                        icon="qrcode-scan"
                        onPress={handleScanQR}
                      />
                    }
                    placeholder="0x... or bc1..."
                  />
                )}
              />
              {errors.recipient && (
                <HelperText type="error">{errors.recipient.message}</HelperText>
              )}
            </View>

            <Divider style={styles.divider} />

            {/* Amount */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Amount</Text>
              <Controller
                control={control}
                name="amount"
                rules={{ validate: validateAmount }}
                render={({ field: { onChange, value } }) => (
                  <TextInput
                    label={`Amount (${selectedToken})`}
                    value={value}
                    onChangeText={onChange}
                    keyboardType="numeric"
                    mode="outlined"
                    style={styles.input}
                    error={!!errors.amount}
                    right={
                      <TextInput.Icon
                        icon="wallet"
                        onPress={handleMaxAmount}
                      />
                    }
                  />
                )}
              />
              {errors.amount && (
                <HelperText type="error">{errors.amount.message}</HelperText>
              )}

              {watchAmount && selectedToken && (
                <View style={styles.amountPreview}>
                  <Text style={styles.previewLabel}>You are sending:</Text>
                  <Text style={styles.previewAmount}>
                    {watchAmount} {selectedToken}
                  </Text>
                </View>
              )}
            </View>

            <Divider style={styles.divider} />

            {/* Note (Optional) */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Note (Optional)</Text>
              <Controller
                control={control}
                name="note"
                render={({ field: { onChange, value } }) => (
                  <TextInput
                    label="Transaction Note"
                    value={value}
                    onChangeText={onChange}
                    mode="outlined"
                    style={styles.input}
                    multiline
                    numberOfLines={2}
                    placeholder="Add a note for this transaction..."
                  />
                )}
              />
            </View>

            {/* Send Button */}
            <Button
              mode="contained"
              onPress={handleSubmit(onSubmit)}
              loading={isSending}
              disabled={isSending || !selectedToken}
              style={styles.sendButton}
              contentStyle={styles.sendButtonContent}
            >
              {isSending ? 'Sending...' : `Send ${selectedToken || 'Crypto'}`}
            </Button>
          </Card.Content>
        </Card>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollView: {
    flex: 1,
  },
  formCard: {
    margin: theme.spacing.md,
    elevation: 2,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: theme.spacing.lg,
    color: theme.colors.onSurface,
  },
  section: {
    marginBottom: theme.spacing.md,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: theme.spacing.sm,
    color: theme.colors.onSurface,
  },
  tokenContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: theme.spacing.sm,
    marginBottom: theme.spacing.sm,
  },
  tokenChip: {
    marginBottom: theme.spacing.xs,
  },
  balanceInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing.sm,
    backgroundColor: theme.colors.surfaceVariant,
    borderRadius: theme.roundness,
  },
  balanceLabel: {
    fontSize: 14,
    color: theme.colors.onSurfaceVariant,
  },
  balanceAmount: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.primary,
  },
  divider: {
    marginVertical: theme.spacing.md,
  },
  input: {
    marginBottom: theme.spacing.xs,
  },
  amountPreview: {
    marginTop: theme.spacing.sm,
    padding: theme.spacing.sm,
    backgroundColor: theme.colors.primaryContainer,
    borderRadius: theme.roundness,
  },
  previewLabel: {
    fontSize: 12,
    color: theme.colors.onPrimaryContainer,
    marginBottom: theme.spacing.xs,
  },
  previewAmount: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.onPrimaryContainer,
  },
  sendButton: {
    marginTop: theme.spacing.lg,
  },
  sendButtonContent: {
    height: 48,
  },
});

export default SendScreen;
