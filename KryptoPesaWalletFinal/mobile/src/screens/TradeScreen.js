import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  RefreshControl,
  TouchableOpacity,
} from 'react-native';
import {
  Text,
  Card,
  Chip,
  Button,
  SegmentedButtons,
  Avatar,
  ProgressBar,
} from 'react-native-paper';
import { useSelector, useDispatch } from 'react-redux';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import { theme } from '../utils/theme';
import { getActiveTrades } from '../store/slices/tradeSlice';
import { selectUser } from '../store/slices/authSlice';
import { socketService } from '../services/socketService';
import LoadingScreen from '../components/LoadingScreen';
import ConnectionStatus from '../components/ConnectionStatus';

const TradeScreen = ({ navigation }) => {
  const dispatch = useDispatch();
  const user = useSelector(selectUser);
  const { activeTrades, isLoading } = useSelector(state => state.trade);

  const [refreshing, setRefreshing] = useState(false);
  const [filter, setFilter] = useState('all'); // 'all', 'buying', 'selling'

  useEffect(() => {
    loadTrades();

    // Join trade rooms for real-time updates
    if (activeTrades && activeTrades.length > 0) {
      activeTrades.forEach(trade => {
        socketService.joinTradeRoom(trade.tradeId);
      });
    }

    return () => {
      // Leave trade rooms when component unmounts
      if (activeTrades && activeTrades.length > 0) {
        activeTrades.forEach(trade => {
          socketService.leaveTradeRoom(trade.tradeId);
        });
      }
    };
  }, [activeTrades]);

  const loadTrades = useCallback(() => {
    dispatch(getActiveTrades());
  }, [dispatch]);

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    loadTrades();
    setTimeout(() => setRefreshing(false), 1000);
  }, [loadTrades]);

  const handleTradePress = (trade) => {
    navigation.navigate('TradeDetails', { trade });
  };

  const getFilteredTrades = () => {
    if (!activeTrades) return [];

    switch (filter) {
      case 'buying':
        return activeTrades.filter(trade => trade.buyer._id === user._id);
      case 'selling':
        return activeTrades.filter(trade => trade.seller._id === user._id);
      default:
        return activeTrades;
    }
  };

  const filterButtons = [
    { value: 'all', label: 'All' },
    { value: 'buying', label: 'Buying' },
    { value: 'selling', label: 'Selling' },
  ];

  if (isLoading && (!activeTrades || activeTrades.length === 0)) {
    return <LoadingScreen message="Loading trades..." />;
  }

  const filteredTrades = getFilteredTrades();

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>My Trades</Text>
          <Text style={styles.headerSubtitle}>
            {filteredTrades.length} active trade{filteredTrades.length !== 1 ? 's' : ''}
          </Text>
        </View>
        <ConnectionStatus />
      </View>

      {/* Filter */}
      <View style={styles.filterContainer}>
        <SegmentedButtons
          value={filter}
          onValueChange={setFilter}
          buttons={filterButtons}
          style={styles.filterButtons}
        />
      </View>

      {/* Trades List */}
      {filteredTrades.length === 0 ? (
        <View style={styles.emptyState}>
          <Icon name="swap-horizontal" size={64} color={theme.colors.onSurfaceVariant} />
          <Text style={styles.emptyTitle}>No Active Trades</Text>
          <Text style={styles.emptySubtitle}>
            {filter === 'all'
              ? 'Start trading by browsing offers or creating your own'
              : `You have no active ${filter} trades`
            }
          </Text>
          <Button
            mode="contained"
            onPress={() => navigation.navigate('Offers')}
            style={styles.browseButton}
          >
            Browse Offers
          </Button>
        </View>
      ) : (
        <FlatList
          data={filteredTrades}
          keyExtractor={(item) => item.tradeId}
          renderItem={({ item }) => (
            <TradeCard
              trade={item}
              currentUserId={user._id}
              onPress={handleTradePress}
            />
          )}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
        />
      )}
    </View>
  );
};

// Trade Card Component
const TradeCard = ({ trade, currentUserId, onPress }) => {
  const isSellerView = trade.seller._id === currentUserId;
  const counterparty = isSellerView ? trade.buyer : trade.seller;
  const role = isSellerView ? 'Selling' : 'Buying';

  const getStatusColor = (status) => {
    switch (status) {
      case 'created': return theme.colors.info;
      case 'funded': return theme.colors.warning;
      case 'payment_sent': return theme.colors.secondary;
      case 'completed': return theme.colors.success;
      case 'disputed': return theme.colors.error;
      case 'cancelled': return theme.colors.onSurfaceVariant;
      default: return theme.colors.onSurfaceVariant;
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'created': return 'Waiting for funding';
      case 'funded': return 'Waiting for payment';
      case 'payment_sent': return 'Payment sent';
      case 'completed': return 'Completed';
      case 'disputed': return 'Disputed';
      case 'cancelled': return 'Cancelled';
      default: return status;
    }
  };

  const getProgressValue = (status) => {
    switch (status) {
      case 'created': return 0.25;
      case 'funded': return 0.5;
      case 'payment_sent': return 0.75;
      case 'completed': return 1.0;
      default: return 0;
    }
  };

  const formatAmount = (amount, decimals = 2) => {
    const num = parseFloat(amount);
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toFixed(decimals);
  };

  const getTimeRemaining = () => {
    if (!trade.expiresAt) return null;

    const now = new Date();
    const expires = new Date(trade.expiresAt);
    const diff = expires - now;

    if (diff <= 0) return 'Expired';

    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    if (hours > 0) return `${hours}h ${minutes}m left`;
    return `${minutes}m left`;
  };

  return (
    <Card style={styles.tradeCard} onPress={() => onPress(trade)}>
      <Card.Content>
        {/* Header */}
        <View style={styles.tradeHeader}>
          <View style={styles.tradeInfo}>
            <Text style={styles.tradeId}>#{trade.tradeId}</Text>
            <Chip
              mode="outlined"
              style={[styles.roleChip, {
                backgroundColor: isSellerView ? theme.colors.sell : theme.colors.buy
              }]}
              textStyle={{ color: 'white', fontWeight: 'bold' }}
            >
              {role}
            </Chip>
          </View>
          <View style={styles.amountInfo}>
            <Text style={styles.cryptoAmount}>
              {formatAmount(trade.cryptocurrency.amount)} {trade.cryptocurrency.symbol}
            </Text>
            <Text style={styles.fiatAmount}>
              {trade.fiat.amount.toFixed(2)} {trade.fiat.currency}
            </Text>
          </View>
        </View>

        {/* Status */}
        <View style={styles.statusSection}>
          <View style={styles.statusInfo}>
            <Chip
              mode="outlined"
              style={[styles.statusChip, { borderColor: getStatusColor(trade.status) }]}
              textStyle={{ color: getStatusColor(trade.status) }}
            >
              {getStatusText(trade.status)}
            </Chip>
            {trade.status !== 'completed' && trade.status !== 'cancelled' && (
              <Text style={styles.timeRemaining}>{getTimeRemaining()}</Text>
            )}
          </View>
          <ProgressBar
            progress={getProgressValue(trade.status)}
            color={getStatusColor(trade.status)}
            style={styles.progressBar}
          />
        </View>

        {/* Counterparty */}
        <View style={styles.counterpartySection}>
          <Avatar.Text
            size={32}
            label={counterparty.username.charAt(0).toUpperCase()}
            style={styles.avatar}
          />
          <View style={styles.counterpartyInfo}>
            <Text style={styles.counterpartyName}>{counterparty.username}</Text>
            <View style={styles.reputation}>
              <Icon name="star" size={14} color={theme.colors.warning} />
              <Text style={styles.reputationScore}>
                {counterparty.reputation.score}% ({counterparty.reputation.completedTrades})
              </Text>
            </View>
          </View>
          <View style={styles.badges}>
            {counterparty.verification?.identity?.verified && (
              <Icon name="check-decagram" size={16} color={theme.colors.success} />
            )}
            {counterparty.verification?.phone?.verified && (
              <Icon name="phone-check" size={16} color={theme.colors.info} />
            )}
          </View>
        </View>

        {/* Action Button */}
        {trade.status !== 'completed' && trade.status !== 'cancelled' && (
          <Button
            mode="contained"
            onPress={() => onPress(trade)}
            style={styles.actionButton}
            contentStyle={styles.actionButtonContent}
          >
            Continue Trade
          </Button>
        )}
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.surface,
    elevation: 2,
  },
  headerContent: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.onSurface,
  },
  headerSubtitle: {
    fontSize: 14,
    color: theme.colors.onSurfaceVariant,
    marginTop: theme.spacing.xs,
  },
  filterContainer: {
    padding: theme.spacing.md,
    backgroundColor: theme.colors.surface,
  },
  filterButtons: {
    backgroundColor: theme.colors.surfaceVariant,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing.xl,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.onSurface,
    marginTop: theme.spacing.md,
    marginBottom: theme.spacing.sm,
  },
  emptySubtitle: {
    fontSize: 14,
    color: theme.colors.onSurfaceVariant,
    textAlign: 'center',
    marginBottom: theme.spacing.lg,
  },
  browseButton: {
    paddingHorizontal: theme.spacing.lg,
  },
  listContainer: {
    padding: theme.spacing.md,
  },
  // Trade Card Styles
  tradeCard: {
    marginBottom: theme.spacing.md,
    elevation: 2,
  },
  tradeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: theme.spacing.md,
  },
  tradeInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.sm,
  },
  tradeId: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.onSurface,
  },
  roleChip: {
    height: 28,
  },
  amountInfo: {
    alignItems: 'flex-end',
  },
  cryptoAmount: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  fiatAmount: {
    fontSize: 14,
    color: theme.colors.onSurfaceVariant,
  },
  statusSection: {
    marginBottom: theme.spacing.md,
  },
  statusInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  statusChip: {
    height: 28,
  },
  timeRemaining: {
    fontSize: 12,
    color: theme.colors.onSurfaceVariant,
    fontStyle: 'italic',
  },
  progressBar: {
    height: 4,
    borderRadius: 2,
  },
  counterpartySection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  avatar: {
    backgroundColor: theme.colors.primary,
  },
  counterpartyInfo: {
    flex: 1,
    marginLeft: theme.spacing.sm,
  },
  counterpartyName: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.onSurface,
  },
  reputation: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.xs,
  },
  reputationScore: {
    fontSize: 12,
    color: theme.colors.onSurfaceVariant,
  },
  badges: {
    flexDirection: 'row',
    gap: theme.spacing.xs,
  },
  actionButton: {
    marginTop: theme.spacing.sm,
  },
  actionButtonContent: {
    height: 40,
  },
});

export default TradeScreen;
