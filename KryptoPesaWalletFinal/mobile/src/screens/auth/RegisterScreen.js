import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, KeyboardAvoidingView, Platform } from 'react-native';
import { Text, TextInput, Button, Card, SegmentedButtons } from 'react-native-paper';
import { useDispatch, useSelector } from 'react-redux';
import { registerUser, selectAuthLoading } from '../../store/slices/authSlice';
import { theme } from '../../utils/theme';

const RegisterScreen = ({ navigation }) => {
  const dispatch = useDispatch();
  const isLoading = useSelector(selectAuthLoading);
  
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    country: 'KE',
    city: '',
  });
  const [showPassword, setShowPassword] = useState(false);

  const countries = [
    { value: 'KE', label: 'Kenya' },
    { value: 'TZ', label: 'Tanzania' },
    { value: 'UG', label: 'Uganda' },
    { value: 'RW', label: 'Rwanda' },
  ];

  const updateField = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleRegister = () => {
    if (formData.password !== formData.confirmPassword) {
      // Show error
      return;
    }
    
    const userData = { ...formData };
    delete userData.confirmPassword;
    
    dispatch(registerUser(userData));
  };

  const isFormValid = () => {
    return Object.values(formData).every(value => value.trim() !== '') &&
           formData.password === formData.confirmPassword &&
           formData.password.length >= 8;
  };

  return (
    <KeyboardAvoidingView 
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.header}>
          <Text style={styles.title}>Create Account</Text>
          <Text style={styles.subtitle}>Join KryptoPesa and start trading</Text>
        </View>

        <Card style={styles.formCard}>
          <Card.Content>
            <View style={styles.row}>
              <TextInput
                label="First Name"
                value={formData.firstName}
                onChangeText={(value) => updateField('firstName', value)}
                mode="outlined"
                style={[styles.input, styles.halfInput]}
              />
              <TextInput
                label="Last Name"
                value={formData.lastName}
                onChangeText={(value) => updateField('lastName', value)}
                mode="outlined"
                style={[styles.input, styles.halfInput]}
              />
            </View>

            <TextInput
              label="Username"
              value={formData.username}
              onChangeText={(value) => updateField('username', value)}
              mode="outlined"
              style={styles.input}
              autoCapitalize="none"
            />

            <TextInput
              label="Email"
              value={formData.email}
              onChangeText={(value) => updateField('email', value)}
              mode="outlined"
              style={styles.input}
              keyboardType="email-address"
              autoCapitalize="none"
            />

            <TextInput
              label="Phone Number (with country code)"
              value={formData.phone}
              onChangeText={(value) => updateField('phone', value)}
              mode="outlined"
              style={styles.input}
              keyboardType="phone-pad"
              placeholder="+254700000000"
            />

            <Text style={styles.sectionLabel}>Country</Text>
            <SegmentedButtons
              value={formData.country}
              onValueChange={(value) => updateField('country', value)}
              buttons={countries}
              style={styles.segmentedButtons}
            />

            <TextInput
              label="City"
              value={formData.city}
              onChangeText={(value) => updateField('city', value)}
              mode="outlined"
              style={styles.input}
            />

            <TextInput
              label="Password"
              value={formData.password}
              onChangeText={(value) => updateField('password', value)}
              mode="outlined"
              secureTextEntry={!showPassword}
              right={
                <TextInput.Icon 
                  icon={showPassword ? "eye-off" : "eye"} 
                  onPress={() => setShowPassword(!showPassword)}
                />
              }
              style={styles.input}
            />

            <TextInput
              label="Confirm Password"
              value={formData.confirmPassword}
              onChangeText={(value) => updateField('confirmPassword', value)}
              mode="outlined"
              secureTextEntry={!showPassword}
              style={styles.input}
            />

            <Button
              mode="contained"
              onPress={handleRegister}
              loading={isLoading}
              disabled={isLoading || !isFormValid()}
              style={styles.registerButton}
            >
              Create Account
            </Button>
          </Card.Content>
        </Card>

        <View style={styles.footer}>
          <Text style={styles.footerText}>Already have an account?</Text>
          <Button
            mode="text"
            onPress={() => navigation.navigate('Login')}
          >
            Sign In
          </Button>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollContent: {
    flexGrow: 1,
    padding: theme.spacing.lg,
  },
  header: {
    alignItems: 'center',
    marginBottom: theme.spacing.lg,
    marginTop: theme.spacing.xl,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: theme.colors.primary,
    marginBottom: theme.spacing.sm,
  },
  subtitle: {
    fontSize: 16,
    color: theme.colors.onSurfaceVariant,
    textAlign: 'center',
  },
  formCard: {
    marginBottom: theme.spacing.lg,
  },
  row: {
    flexDirection: 'row',
    gap: theme.spacing.sm,
  },
  input: {
    marginBottom: theme.spacing.md,
  },
  halfInput: {
    flex: 1,
  },
  sectionLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: theme.spacing.sm,
    color: theme.colors.onSurface,
  },
  segmentedButtons: {
    marginBottom: theme.spacing.md,
  },
  registerButton: {
    marginTop: theme.spacing.md,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: theme.spacing.xl,
  },
  footerText: {
    color: theme.colors.onSurfaceVariant,
  },
});

export default RegisterScreen;
