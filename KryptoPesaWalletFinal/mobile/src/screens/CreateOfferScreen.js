import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import {
  Text,
  TextInput,
  Button,
  SegmentedButtons,
  Card,
  Chip,
  Divider,
  Switch,
  HelperText,
} from 'react-native-paper';
import { useSelector, useDispatch } from 'react-redux';
import { useForm, Controller } from 'react-hook-form';

import { theme } from '../utils/theme';
import { createOffer } from '../store/slices/offerSlice';
import { selectUser } from '../store/slices/authSlice';
import { selectOfferCreating } from '../store/slices/offerSlice';

const CreateOfferScreen = ({ navigation }) => {
  const dispatch = useDispatch();
  const user = useSelector(selectUser);
  const isCreating = useSelector(selectOfferCreating);

  const { control, handleSubmit, watch, setValue, formState: { errors } } = useForm({
    defaultValues: {
      type: 'sell',
      cryptocurrency: 'USDT',
      minAmount: '',
      maxAmount: '',
      availableAmount: '',
      fiatCurrency: user?.profile?.location?.country === 'KE' ? 'KES' : 'TZS',
      priceType: 'margin',
      fixedPrice: '',
      marginPercentage: '2.5',
      paymentMethods: [],
      timeLimit: '30',
      instructions: '',
      autoReply: false,
    }
  });

  const [selectedPaymentMethods, setSelectedPaymentMethods] = useState([]);

  const watchType = watch('type');
  const watchCryptocurrency = watch('cryptocurrency');
  const watchPriceType = watch('priceType');

  const tradeTypes = [
    { value: 'sell', label: 'Sell' },
    { value: 'buy', label: 'Buy' },
  ];

  const cryptocurrencies = [
    { value: 'USDT', label: 'USDT' },
    { value: 'USDC', label: 'USDC' },
    { value: 'BTC', label: 'BTC' },
    { value: 'ETH', label: 'ETH' },
    { value: 'DAI', label: 'DAI' },
  ];

  const fiatCurrencies = [
    { value: 'KES', label: 'KES - Kenyan Shilling' },
    { value: 'TZS', label: 'TZS - Tanzanian Shilling' },
    { value: 'UGX', label: 'UGX - Ugandan Shilling' },
    { value: 'RWF', label: 'RWF - Rwandan Franc' },
    { value: 'USD', label: 'USD - US Dollar' },
  ];

  const priceTypes = [
    { value: 'margin', label: 'Market +/- %' },
    { value: 'fixed', label: 'Fixed Price' },
  ];

  const paymentMethods = [
    { id: 'mobile_money', label: 'Mobile Money', icon: 'cellphone' },
    { id: 'bank_transfer', label: 'Bank Transfer', icon: 'bank' },
    { id: 'cash', label: 'Cash Meeting', icon: 'cash' },
    { id: 'other', label: 'Other', icon: 'dots-horizontal' },
  ];

  const togglePaymentMethod = (methodId) => {
    const newMethods = selectedPaymentMethods.includes(methodId)
      ? selectedPaymentMethods.filter(id => id !== methodId)
      : [...selectedPaymentMethods, methodId];

    setSelectedPaymentMethods(newMethods);
    setValue('paymentMethods', newMethods.map(id => ({ method: id })));
  };

  const onSubmit = async (data) => {
    try {
      // Validation
      if (parseFloat(data.minAmount) >= parseFloat(data.maxAmount)) {
        Alert.alert('Invalid Amount', 'Minimum amount must be less than maximum amount');
        return;
      }

      if (parseFloat(data.availableAmount) < parseFloat(data.maxAmount)) {
        Alert.alert('Invalid Amount', 'Available amount must be at least equal to maximum amount');
        return;
      }

      if (selectedPaymentMethods.length === 0) {
        Alert.alert('Payment Methods Required', 'Please select at least one payment method');
        return;
      }

      // Prepare offer data
      const offerData = {
        type: data.type,
        cryptocurrency: {
          symbol: data.cryptocurrency,
          minAmount: data.minAmount,
          maxAmount: data.maxAmount,
          availableAmount: data.availableAmount,
        },
        fiat: {
          currency: data.fiatCurrency,
          priceType: data.priceType,
          ...(data.priceType === 'fixed'
            ? { fixedPrice: parseFloat(data.fixedPrice) }
            : { marginPercentage: parseFloat(data.marginPercentage) }
          ),
        },
        paymentMethods: selectedPaymentMethods.map(methodId => ({
          method: methodId,
          details: {} // Will be filled in later
        })),
        terms: {
          timeLimit: parseInt(data.timeLimit),
          instructions: data.instructions,
          autoReply: data.autoReply,
        },
      };

      const result = await dispatch(createOffer(offerData));

      if (createOffer.fulfilled.match(result)) {
        navigation.goBack();
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to create offer. Please try again.');
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <Card style={styles.formCard}>
          <Card.Content>
            <Text style={styles.sectionTitle}>Create Trading Offer</Text>

            {/* Trade Type */}
            <View style={styles.formSection}>
              <Text style={styles.fieldLabel}>I want to:</Text>
              <Controller
                control={control}
                name="type"
                render={({ field: { onChange, value } }) => (
                  <SegmentedButtons
                    value={value}
                    onValueChange={onChange}
                    buttons={tradeTypes}
                    style={styles.segmentedButtons}
                  />
                )}
              />
            </View>

            <Divider style={styles.divider} />

            {/* Cryptocurrency */}
            <View style={styles.formSection}>
              <Text style={styles.fieldLabel}>Cryptocurrency:</Text>
              <View style={styles.chipContainer}>
                {cryptocurrencies.map((crypto) => (
                  <Controller
                    key={crypto.value}
                    control={control}
                    name="cryptocurrency"
                    render={({ field: { onChange, value } }) => (
                      <Chip
                        selected={value === crypto.value}
                        onPress={() => onChange(crypto.value)}
                        style={styles.chip}
                      >
                        {crypto.label}
                      </Chip>
                    )}
                  />
                ))}
              </View>
            </View>

            <Divider style={styles.divider} />

            {/* Amount Settings */}
            <View style={styles.formSection}>
              <Text style={styles.fieldLabel}>Amount Settings:</Text>

              <View style={styles.amountRow}>
                <Controller
                  control={control}
                  name="minAmount"
                  rules={{ required: 'Minimum amount is required' }}
                  render={({ field: { onChange, value } }) => (
                    <TextInput
                      label="Min Amount"
                      value={value}
                      onChangeText={onChange}
                      keyboardType="numeric"
                      style={styles.amountInput}
                      mode="outlined"
                      dense
                      error={!!errors.minAmount}
                    />
                  )}
                />

                <Controller
                  control={control}
                  name="maxAmount"
                  rules={{ required: 'Maximum amount is required' }}
                  render={({ field: { onChange, value } }) => (
                    <TextInput
                      label="Max Amount"
                      value={value}
                      onChangeText={onChange}
                      keyboardType="numeric"
                      style={styles.amountInput}
                      mode="outlined"
                      dense
                      error={!!errors.maxAmount}
                    />
                  )}
                />
              </View>

              <Controller
                control={control}
                name="availableAmount"
                rules={{ required: 'Available amount is required' }}
                render={({ field: { onChange, value } }) => (
                  <TextInput
                    label={`Total Available ${watchCryptocurrency}`}
                    value={value}
                    onChangeText={onChange}
                    keyboardType="numeric"
                    style={styles.fullWidthInput}
                    mode="outlined"
                    dense
                    error={!!errors.availableAmount}
                  />
                )}
              />
            </View>
            <Divider style={styles.divider} />

            {/* Pricing */}
            <View style={styles.formSection}>
              <Text style={styles.fieldLabel}>Pricing:</Text>

              <Controller
                control={control}
                name="fiatCurrency"
                render={({ field: { onChange, value } }) => (
                  <View style={styles.chipContainer}>
                    {fiatCurrencies.map((currency) => (
                      <Chip
                        key={currency.value}
                        selected={value === currency.value}
                        onPress={() => onChange(currency.value)}
                        style={styles.chip}
                      >
                        {currency.value}
                      </Chip>
                    ))}
                  </View>
                )}
              />

              <Controller
                control={control}
                name="priceType"
                render={({ field: { onChange, value } }) => (
                  <SegmentedButtons
                    value={value}
                    onValueChange={onChange}
                    buttons={priceTypes}
                    style={styles.segmentedButtons}
                  />
                )}
              />

              {watchPriceType === 'fixed' ? (
                <Controller
                  control={control}
                  name="fixedPrice"
                  rules={{ required: 'Fixed price is required' }}
                  render={({ field: { onChange, value } }) => (
                    <TextInput
                      label={`Fixed Price per ${watchCryptocurrency}`}
                      value={value}
                      onChangeText={onChange}
                      keyboardType="numeric"
                      style={styles.fullWidthInput}
                      mode="outlined"
                      dense
                      error={!!errors.fixedPrice}
                    />
                  )}
                />
              ) : (
                <Controller
                  control={control}
                  name="marginPercentage"
                  rules={{ required: 'Margin percentage is required' }}
                  render={({ field: { onChange, value } }) => (
                    <TextInput
                      label="Margin % (+ above market, - below market)"
                      value={value}
                      onChangeText={onChange}
                      keyboardType="numeric"
                      style={styles.fullWidthInput}
                      mode="outlined"
                      dense
                      error={!!errors.marginPercentage}
                      right={<TextInput.Affix text="%" />}
                    />
                  )}
                />
              )}
            </View>

            <Divider style={styles.divider} />

            {/* Payment Methods */}
            <View style={styles.formSection}>
              <Text style={styles.fieldLabel}>Payment Methods:</Text>
              <Text style={styles.fieldHelper}>Select all payment methods you accept</Text>

              <View style={styles.paymentMethodsContainer}>
                {paymentMethods.map((method) => (
                  <Chip
                    key={method.id}
                    selected={selectedPaymentMethods.includes(method.id)}
                    onPress={() => togglePaymentMethod(method.id)}
                    style={styles.paymentMethodChip}
                    icon={method.icon}
                  >
                    {method.label}
                  </Chip>
                ))}
              </View>
            </View>

            <Divider style={styles.divider} />

            {/* Terms */}
            <View style={styles.formSection}>
              <Text style={styles.fieldLabel}>Trading Terms:</Text>

              <Controller
                control={control}
                name="timeLimit"
                render={({ field: { onChange, value } }) => (
                  <TextInput
                    label="Payment Time Limit (minutes)"
                    value={value}
                    onChangeText={onChange}
                    keyboardType="numeric"
                    style={styles.fullWidthInput}
                    mode="outlined"
                    dense
                    right={<TextInput.Affix text="min" />}
                  />
                )}
              />

              <Controller
                control={control}
                name="instructions"
                render={({ field: { onChange, value } }) => (
                  <TextInput
                    label="Trading Instructions (optional)"
                    value={value}
                    onChangeText={onChange}
                    multiline
                    numberOfLines={3}
                    style={styles.fullWidthInput}
                    mode="outlined"
                    placeholder="Provide any specific instructions for traders..."
                  />
                )}
              />

              <View style={styles.switchRow}>
                <Text style={styles.switchLabel}>Auto-reply to messages</Text>
                <Controller
                  control={control}
                  name="autoReply"
                  render={({ field: { onChange, value } }) => (
                    <Switch value={value} onValueChange={onChange} />
                  )}
                />
              </View>
            </View>

            {/* Submit Button */}
            <Button
              mode="contained"
              onPress={handleSubmit(onSubmit)}
              loading={isCreating}
              disabled={isCreating}
              style={styles.submitButton}
              contentStyle={styles.submitButtonContent}
            >
              {isCreating ? 'Creating Offer...' : 'Create Offer'}
            </Button>
          </Card.Content>
        </Card>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollView: {
    flex: 1,
  },
  formCard: {
    margin: theme.spacing.md,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: theme.spacing.lg,
    color: theme.colors.onSurface,
  },
  formSection: {
    marginBottom: theme.spacing.md,
  },
  fieldLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: theme.spacing.sm,
    color: theme.colors.onSurface,
  },
  fieldHelper: {
    fontSize: 12,
    color: theme.colors.onSurfaceVariant,
    marginBottom: theme.spacing.sm,
  },
  segmentedButtons: {
    marginBottom: theme.spacing.sm,
  },
  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: theme.spacing.sm,
    marginBottom: theme.spacing.sm,
  },
  chip: {
    marginBottom: theme.spacing.xs,
  },
  divider: {
    marginVertical: theme.spacing.md,
  },
  amountRow: {
    flexDirection: 'row',
    gap: theme.spacing.sm,
    marginBottom: theme.spacing.sm,
  },
  amountInput: {
    flex: 1,
  },
  fullWidthInput: {
    marginBottom: theme.spacing.sm,
  },
  paymentMethodsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: theme.spacing.sm,
  },
  paymentMethodChip: {
    marginBottom: theme.spacing.xs,
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: theme.spacing.sm,
  },
  switchLabel: {
    fontSize: 14,
    color: theme.colors.onSurface,
  },
  submitButton: {
    marginTop: theme.spacing.lg,
  },
  submitButtonContent: {
    height: 48,
  },
});

export default CreateOfferScreen;
