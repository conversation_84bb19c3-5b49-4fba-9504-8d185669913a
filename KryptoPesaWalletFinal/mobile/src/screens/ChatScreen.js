import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import {
  Text,
  TextInput,
  IconButton,
  Avatar,
  Surface,
  Chip,
  Button,
} from 'react-native-paper';
import { useSelector, useDispatch } from 'react-redux';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import { theme } from '../utils/theme';
import { selectUser } from '../store/slices/authSlice';
import {
  getChatMessages,
  sendChatMessage,
  markMessagesAsRead,
  setActiveChat,
  selectChatMessages,
  selectChatLoading,
  selectChatSending,
  selectTypingUsers,
} from '../store/slices/chatSlice';
import { socketService } from '../services/socketService';
import LoadingScreen from '../components/LoadingScreen';
import ConnectionStatus from '../components/ConnectionStatus';

const ChatScreen = ({ navigation, route }) => {
  const dispatch = useDispatch();
  const user = useSelector(selectUser);
  const { trade } = route.params;

  const messages = useSelector(state => selectChatMessages(state, trade.tradeId));
  const isLoading = useSelector(selectChatLoading);
  const isSending = useSelector(selectChatSending);
  const typingUsers = useSelector(state => selectTypingUsers(state, trade.tradeId));

  const [messageText, setMessageText] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const flatListRef = useRef(null);
  const typingTimeoutRef = useRef(null);

  const counterparty = trade.buyer._id === user._id ? trade.seller : trade.buyer;

  useEffect(() => {
    // Set active chat
    dispatch(setActiveChat(trade.tradeId));

    // Load messages
    dispatch(getChatMessages({ tradeId: trade.tradeId }));

    // Join trade room for real-time updates
    socketService.joinTradeRoom(trade.tradeId);

    return () => {
      // Leave trade room and clear active chat
      socketService.leaveTradeRoom(trade.tradeId);
      dispatch(setActiveChat(null));

      // Clear typing timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, [trade.tradeId, dispatch]);

  useEffect(() => {
    // Mark messages as read when messages change
    if (messages.length > 0) {
      const unreadMessages = messages
        .filter(msg => msg.sender._id !== user._id && !msg.isRead)
        .map(msg => msg._id);

      if (unreadMessages.length > 0) {
        dispatch(markMessagesAsRead({
          tradeId: trade.tradeId,
          messageIds: unreadMessages
        }));
        socketService.markMessagesAsRead(trade.tradeId, unreadMessages);
      }
    }
  }, [messages, user._id, trade.tradeId, dispatch]);

  const handleSendMessage = useCallback(async () => {
    if (!messageText.trim()) return;

    const content = messageText.trim();
    setMessageText('');

    // Stop typing indicator
    if (isTyping) {
      setIsTyping(false);
      socketService.sendTypingStop(trade.tradeId);
    }

    try {
      // Send via socket for real-time delivery
      socketService.sendMessage(trade.tradeId, content);

      // Also send via API for persistence
      await dispatch(sendChatMessage({
        tradeId: trade.tradeId,
        content,
        type: 'text'
      }));
    } catch (error) {
      Alert.alert('Error', 'Failed to send message. Please try again.');
      setMessageText(content); // Restore message text
    }
  }, [messageText, isTyping, trade.tradeId, dispatch]);

  const handleTyping = useCallback((text) => {
    setMessageText(text);

    // Handle typing indicators
    if (text.length > 0 && !isTyping) {
      setIsTyping(true);
      socketService.sendTypingStart(trade.tradeId);
    }

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set new timeout to stop typing indicator
    typingTimeoutRef.current = setTimeout(() => {
      if (isTyping) {
        setIsTyping(false);
        socketService.sendTypingStop(trade.tradeId);
      }
    }, 2000);
  }, [isTyping, trade.tradeId]);

  const renderMessage = ({ item: message, index }) => {
    const isOwnMessage = message.sender._id === user._id;
    const showAvatar = !isOwnMessage && (
      index === messages.length - 1 ||
      messages[index + 1]?.sender._id !== message.sender._id
    );

    return (
      <View style={[
        styles.messageContainer,
        isOwnMessage ? styles.ownMessageContainer : styles.otherMessageContainer
      ]}>
        {showAvatar && (
          <Avatar.Text
            size={32}
            label={message.sender.username.charAt(0).toUpperCase()}
            style={styles.messageAvatar}
          />
        )}

        <Surface style={[
          styles.messageBubble,
          isOwnMessage ? styles.ownMessageBubble : styles.otherMessageBubble,
          !showAvatar && !isOwnMessage && styles.messageBubbleNoAvatar
        ]}>
          <Text style={[
            styles.messageText,
            isOwnMessage ? styles.ownMessageText : styles.otherMessageText
          ]}>
            {message.content}
          </Text>

          <View style={styles.messageFooter}>
            <Text style={[
              styles.messageTime,
              isOwnMessage ? styles.ownMessageTime : styles.otherMessageTime
            ]}>
              {formatMessageTime(message.createdAt)}
            </Text>

            {isOwnMessage && (
              <Icon
                name={message.isRead ? "check-all" : "check"}
                size={14}
                color={message.isRead ? theme.colors.primary : theme.colors.onSurfaceVariant}
                style={styles.messageStatus}
              />
            )}
          </View>
        </Surface>
      </View>
    );
  };

  const renderTypingIndicator = () => {
    if (typingUsers.length === 0) return null;

    return (
      <View style={styles.typingContainer}>
        <Avatar.Text
          size={24}
          label={counterparty.username.charAt(0).toUpperCase()}
          style={styles.typingAvatar}
        />
        <Surface style={styles.typingBubble}>
          <Text style={styles.typingText}>
            {counterparty.username} is typing...
          </Text>
        </Surface>
      </View>
    );
  };

  const formatMessageTime = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  if (isLoading && messages.length === 0) {
    return <LoadingScreen message="Loading chat..." />;
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
    >
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <IconButton
            icon="arrow-left"
            size={24}
            onPress={() => navigation.goBack()}
          />
          <Avatar.Text
            size={40}
            label={counterparty.username.charAt(0).toUpperCase()}
            style={styles.headerAvatar}
          />
          <View style={styles.headerInfo}>
            <Text style={styles.headerName}>{counterparty.username}</Text>
            <Text style={styles.headerSubtitle}>Trade #{trade.tradeId}</Text>
          </View>
        </View>
        <ConnectionStatus showDetails />
      </View>

      {/* Trade Status */}
      <View style={styles.tradeStatus}>
        <Chip
          mode="outlined"
          style={styles.statusChip}
          textStyle={styles.statusText}
        >
          {trade.status.replace('_', ' ').toUpperCase()}
        </Chip>
        <Text style={styles.tradeAmount}>
          {trade.cryptocurrency.amount} {trade.cryptocurrency.symbol}
        </Text>
      </View>

      {/* Messages */}
      <FlatList
        ref={flatListRef}
        data={messages}
        keyExtractor={(item) => item._id}
        renderItem={renderMessage}
        style={styles.messagesList}
        contentContainerStyle={styles.messagesContent}
        inverted
        showsVerticalScrollIndicator={false}
        ListHeaderComponent={renderTypingIndicator}
      />

      {/* Input */}
      <View style={styles.inputContainer}>
        <TextInput
          value={messageText}
          onChangeText={handleTyping}
          placeholder="Type a message..."
          mode="outlined"
          style={styles.textInput}
          multiline
          maxLength={1000}
          right={
            <TextInput.Icon
              icon="send"
              onPress={handleSendMessage}
              disabled={!messageText.trim() || isSending}
            />
          }
        />
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.sm,
    backgroundColor: theme.colors.surface,
    elevation: 2,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  headerAvatar: {
    backgroundColor: theme.colors.primary,
    marginRight: theme.spacing.sm,
  },
  headerInfo: {
    flex: 1,
  },
  headerName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.onSurface,
  },
  headerSubtitle: {
    fontSize: 12,
    color: theme.colors.onSurfaceVariant,
  },
  tradeStatus: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    backgroundColor: theme.colors.surfaceVariant,
  },
  statusChip: {
    height: 24,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  tradeAmount: {
    fontSize: 14,
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  messagesList: {
    flex: 1,
    paddingHorizontal: theme.spacing.md,
  },
  messagesContent: {
    paddingVertical: theme.spacing.md,
  },
  messageContainer: {
    flexDirection: 'row',
    marginBottom: theme.spacing.sm,
    alignItems: 'flex-end',
  },
  ownMessageContainer: {
    justifyContent: 'flex-end',
  },
  otherMessageContainer: {
    justifyContent: 'flex-start',
  },
  messageAvatar: {
    backgroundColor: theme.colors.primary,
    marginRight: theme.spacing.xs,
  },
  messageBubble: {
    maxWidth: '75%',
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: 16,
    elevation: 1,
  },
  ownMessageBubble: {
    backgroundColor: theme.colors.primary,
    borderBottomRightRadius: 4,
  },
  otherMessageBubble: {
    backgroundColor: theme.colors.surface,
    borderBottomLeftRadius: 4,
  },
  messageBubbleNoAvatar: {
    marginLeft: 40, // Avatar width + margin
  },
  messageText: {
    fontSize: 14,
    lineHeight: 18,
  },
  ownMessageText: {
    color: theme.colors.onPrimary,
  },
  otherMessageText: {
    color: theme.colors.onSurface,
  },
  messageFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    marginTop: theme.spacing.xs,
  },
  messageTime: {
    fontSize: 10,
    marginRight: theme.spacing.xs,
  },
  ownMessageTime: {
    color: theme.colors.onPrimary,
    opacity: 0.7,
  },
  otherMessageTime: {
    color: theme.colors.onSurfaceVariant,
  },
  messageStatus: {
    marginLeft: theme.spacing.xs,
  },
  typingContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    marginBottom: theme.spacing.sm,
  },
  typingAvatar: {
    backgroundColor: theme.colors.primary,
    marginRight: theme.spacing.xs,
  },
  typingBubble: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: 16,
    backgroundColor: theme.colors.surface,
    elevation: 1,
  },
  typingText: {
    fontSize: 12,
    fontStyle: 'italic',
    color: theme.colors.onSurfaceVariant,
  },
  inputContainer: {
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    backgroundColor: theme.colors.surface,
    elevation: 2,
  },
  textInput: {
    backgroundColor: theme.colors.background,
    maxHeight: 100,
  },
});

export default ChatScreen;
