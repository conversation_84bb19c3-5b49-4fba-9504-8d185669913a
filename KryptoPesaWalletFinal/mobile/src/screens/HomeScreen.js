import React, { useEffect } from 'react';
import { View, StyleSheet, ScrollView, RefreshControl } from 'react-native';
import { Text, Card, Button } from 'react-native-paper';
import { useSelector, useDispatch } from 'react-redux';
import { selectUser } from '../store/slices/authSlice';
import { selectBalances, refreshBalances } from '../store/slices/walletSlice';
import { getActiveTrades } from '../store/slices/tradeSlice';
import { theme } from '../utils/theme';

const HomeScreen = ({ navigation }) => {
  const dispatch = useDispatch();
  const user = useSelector(selectUser);
  const balances = useSelector(selectBalances);
  const [refreshing, setRefreshing] = React.useState(false);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    dispatch(refreshBalances());
    dispatch(getActiveTrades());
  };

  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    loadData();
    setTimeout(() => setRefreshing(false), 1000);
  }, []);

  const getTotalBalance = () => {
    return balances.reduce((total, balance) => {
      // Simplified calculation - would use real exchange rates
      const amount = parseFloat(balance.balance) / Math.pow(10, balance.decimals);
      return total + amount;
    }, 0);
  };

  return (
    <ScrollView 
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.greeting}>
          Welcome back, {user?.profile?.firstName}!
        </Text>
        <Text style={styles.subtitle}>
          Ready to trade crypto?
        </Text>
      </View>

      {/* Portfolio Overview */}
      <Card style={styles.portfolioCard}>
        <Card.Content>
          <Text style={styles.portfolioLabel}>Total Portfolio Value</Text>
          <Text style={styles.portfolioValue}>
            ${getTotalBalance().toFixed(2)}
          </Text>
          <View style={styles.portfolioActions}>
            <Button 
              mode="contained" 
              onPress={() => navigation.navigate('Wallet')}
              style={styles.actionButton}
            >
              View Wallet
            </Button>
            <Button 
              mode="outlined" 
              onPress={() => navigation.navigate('Trade')}
              style={styles.actionButton}
            >
              Start Trading
            </Button>
          </View>
        </Card.Content>
      </Card>

      {/* Quick Actions */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Quick Actions</Text>
        <View style={styles.quickActions}>
          <Card style={styles.quickActionCard}>
            <Card.Content style={styles.quickActionContent}>
              <Text style={styles.quickActionTitle}>Buy Crypto</Text>
              <Button 
                mode="contained" 
                onPress={() => navigation.navigate('Offers')}
                compact
              >
                Browse Offers
              </Button>
            </Card.Content>
          </Card>
          
          <Card style={styles.quickActionCard}>
            <Card.Content style={styles.quickActionContent}>
              <Text style={styles.quickActionTitle}>Sell Crypto</Text>
              <Button 
                mode="contained" 
                onPress={() => navigation.navigate('CreateOffer')}
                compact
              >
                Create Offer
              </Button>
            </Card.Content>
          </Card>
        </View>
      </View>

      {/* Recent Activity */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Recent Activity</Text>
        <Card style={styles.activityCard}>
          <Card.Content>
            <Text style={styles.emptyState}>
              No recent activity. Start trading to see your activity here.
            </Text>
          </Card.Content>
        </Card>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.primary,
  },
  greeting: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.onPrimary,
    marginBottom: theme.spacing.xs,
  },
  subtitle: {
    fontSize: 16,
    color: theme.colors.onPrimary,
    opacity: 0.8,
  },
  portfolioCard: {
    margin: theme.spacing.md,
    marginTop: -theme.spacing.lg,
    elevation: 4,
  },
  portfolioLabel: {
    fontSize: 14,
    color: theme.colors.onSurfaceVariant,
    marginBottom: theme.spacing.xs,
  },
  portfolioValue: {
    fontSize: 32,
    fontWeight: 'bold',
    color: theme.colors.primary,
    marginBottom: theme.spacing.md,
  },
  portfolioActions: {
    flexDirection: 'row',
    gap: theme.spacing.sm,
  },
  actionButton: {
    flex: 1,
  },
  section: {
    padding: theme.spacing.md,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: theme.spacing.md,
    color: theme.colors.onBackground,
  },
  quickActions: {
    flexDirection: 'row',
    gap: theme.spacing.sm,
  },
  quickActionCard: {
    flex: 1,
  },
  quickActionContent: {
    alignItems: 'center',
  },
  quickActionTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: theme.spacing.sm,
    textAlign: 'center',
  },
  activityCard: {
    padding: theme.spacing.md,
  },
  emptyState: {
    textAlign: 'center',
    color: theme.colors.onSurfaceVariant,
    fontStyle: 'italic',
  },
});

export default HomeScreen;
