// Mock notification service for Expo Go testing
// This provides the same interface but with console logging instead of actual notifications

class MockNotificationService {
  constructor() {
    this.isInitialized = false;
    this.fcmToken = 'mock-token-for-expo-go';
    this.notificationListeners = [];
  }

  async initialize() {
    console.log('📱 Mock Notification Service initialized for Expo Go');
    this.isInitialized = true;
    return true;
  }

  async requestPermissions() {
    console.log('📱 Mock: Notification permissions granted');
    return true;
  }

  async showLocalNotification({ title, body, data = {} }) {
    console.log('📱 Mock Notification:', { title, body, data });
    // In a real app, this would show a notification
    // For testing, we'll just log it
  }

  addNotificationListener(callback) {
    this.notificationListeners.push(callback);
    console.log('📱 Mock: Notification listener added');
  }

  removeNotificationListener(callback) {
    this.notificationListeners = this.notificationListeners.filter(cb => cb !== callback);
    console.log('📱 Mock: Notification listener removed');
  }

  getFCMToken() {
    return this.fcmToken;
  }

  async areNotificationsEnabled() {
    return true;
  }

  async openNotificationSettings() {
    console.log('📱 Mock: Would open notification settings');
  }

  async clearAllNotifications() {
    console.log('📱 Mock: All notifications cleared');
  }

  async getBadgeCount() {
    return 0;
  }

  async setBadgeCount(count) {
    console.log('📱 Mock: Badge count set to', count);
  }
}

// Create singleton instance
export const notificationService = new MockNotificationService();

// Export notification types for reference
export const NOTIFICATION_TYPES = {
  TRADE_UPDATE: 'trade_update',
  NEW_MESSAGE: 'new_message',
  SYSTEM_ALERT: 'system_alert',
};
