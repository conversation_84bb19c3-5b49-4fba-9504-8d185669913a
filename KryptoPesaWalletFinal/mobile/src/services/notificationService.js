import messaging from '@react-native-firebase/messaging';
import notifee, { 
  AndroidImportance, 
  AndroidVisibility,
  TriggerType,
  RepeatFrequency 
} from '@notifee/react-native';
import { Platform, Alert, Linking } from 'react-native';
import { store } from '../store';
import { selectUser } from '../store/slices/authSlice';

class NotificationService {
  constructor() {
    this.isInitialized = false;
    this.fcmToken = null;
    this.notificationListeners = [];
  }

  // Initialize notification service
  async initialize() {
    if (this.isInitialized) return;

    try {
      // Request permissions
      const hasPermission = await this.requestPermissions();
      if (!hasPermission) {
        console.log('Notification permissions not granted');
        return false;
      }

      // Initialize Firebase messaging
      await this.initializeFirebase();
      
      // Initialize Notifee for local notifications
      await this.initializeNotifee();
      
      // Setup listeners
      this.setupListeners();
      
      this.isInitialized = true;
      console.log('Notification service initialized successfully');
      return true;
    } catch (error) {
      console.log('Failed to initialize notification service:', error);
      return false;
    }
  }

  // Request notification permissions
  async requestPermissions() {
    try {
      if (Platform.OS === 'ios') {
        const authStatus = await messaging().requestPermission();
        return authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
               authStatus === messaging.AuthorizationStatus.PROVISIONAL;
      } else {
        // Android permissions are handled by Notifee
        const settings = await notifee.requestPermission();
        return settings.authorizationStatus === 1; // AUTHORIZED
      }
    } catch (error) {
      console.log('Permission request error:', error);
      return false;
    }
  }

  // Initialize Firebase Cloud Messaging
  async initializeFirebase() {
    try {
      // Get FCM token
      this.fcmToken = await messaging().getToken();
      console.log('FCM Token:', this.fcmToken);
      
      // Send token to backend
      await this.registerTokenWithBackend(this.fcmToken);
      
      // Listen for token refresh
      messaging().onTokenRefresh(async (token) => {
        console.log('FCM Token refreshed:', token);
        this.fcmToken = token;
        await this.registerTokenWithBackend(token);
      });
      
    } catch (error) {
      console.log('Firebase initialization error:', error);
    }
  }

  // Initialize Notifee for local notifications
  async initializeNotifee() {
    try {
      // Create notification channels for Android
      if (Platform.OS === 'android') {
        await this.createNotificationChannels();
      }
    } catch (error) {
      console.log('Notifee initialization error:', error);
    }
  }

  // Create notification channels for Android
  async createNotificationChannels() {
    const channels = [
      {
        id: 'trade_updates',
        name: 'Trade Updates',
        description: 'Notifications about trade status changes',
        importance: AndroidImportance.HIGH,
        visibility: AndroidVisibility.PUBLIC,
      },
      {
        id: 'new_messages',
        name: 'New Messages',
        description: 'Notifications for new chat messages',
        importance: AndroidImportance.HIGH,
        visibility: AndroidVisibility.PRIVATE,
      },
      {
        id: 'system_alerts',
        name: 'System Alerts',
        description: 'Important system notifications',
        importance: AndroidImportance.HIGH,
        visibility: AndroidVisibility.PUBLIC,
      },
    ];

    for (const channel of channels) {
      await notifee.createChannel(channel);
    }
  }

  // Setup notification listeners
  setupListeners() {
    // Foreground message handler
    messaging().onMessage(async (remoteMessage) => {
      console.log('Foreground message received:', remoteMessage);
      await this.handleForegroundMessage(remoteMessage);
    });

    // Background message handler
    messaging().setBackgroundMessageHandler(async (remoteMessage) => {
      console.log('Background message received:', remoteMessage);
      await this.handleBackgroundMessage(remoteMessage);
    });

    // Notification opened app from background/quit state
    messaging().onNotificationOpenedApp((remoteMessage) => {
      console.log('Notification opened app:', remoteMessage);
      this.handleNotificationPress(remoteMessage);
    });

    // Check if app was opened from a notification (cold start)
    messaging().getInitialNotification().then((remoteMessage) => {
      if (remoteMessage) {
        console.log('App opened from notification (cold start):', remoteMessage);
        this.handleNotificationPress(remoteMessage);
      }
    });

    // Notifee notification press handler
    notifee.onForegroundEvent(({ type, detail }) => {
      if (type === 1) { // PRESS
        this.handleLocalNotificationPress(detail.notification);
      }
    });
  }

  // Handle foreground messages
  async handleForegroundMessage(remoteMessage) {
    const { notification, data } = remoteMessage;
    
    // Show local notification for foreground messages
    await this.showLocalNotification({
      title: notification?.title || 'KryptoPesa',
      body: notification?.body || 'You have a new notification',
      data: data || {},
      channelId: this.getChannelId(data?.type),
    });
  }

  // Handle background messages
  async handleBackgroundMessage(remoteMessage) {
    const { notification, data } = remoteMessage;
    
    // Background messages are automatically displayed by Firebase
    // We can perform background tasks here if needed
    console.log('Processing background message:', data);
  }

  // Show local notification
  async showLocalNotification({ title, body, data = {}, channelId = 'system_alerts' }) {
    try {
      await notifee.displayNotification({
        title,
        body,
        data,
        android: {
          channelId,
          importance: AndroidImportance.HIGH,
          pressAction: {
            id: 'default',
          },
        },
        ios: {
          sound: 'default',
          badge: 1,
        },
      });
    } catch (error) {
      console.log('Local notification error:', error);
    }
  }

  // Handle notification press
  handleNotificationPress(remoteMessage) {
    const { data } = remoteMessage;
    
    // Notify listeners
    this.notificationListeners.forEach(listener => {
      try {
        listener('press', data);
      } catch (error) {
        console.log('Notification listener error:', error);
      }
    });
  }

  // Handle local notification press
  handleLocalNotificationPress(notification) {
    const { data } = notification;
    
    // Notify listeners
    this.notificationListeners.forEach(listener => {
      try {
        listener('press', data);
      } catch (error) {
        console.log('Local notification listener error:', error);
      }
    });
  }

  // Get appropriate channel ID based on notification type
  getChannelId(type) {
    switch (type) {
      case 'trade_update':
        return 'trade_updates';
      case 'new_message':
        return 'new_messages';
      case 'system_alert':
        return 'system_alerts';
      default:
        return 'system_alerts';
    }
  }

  // Register FCM token with backend
  async registerTokenWithBackend(token) {
    try {
      const user = selectUser(store.getState());
      if (!user) return;

      // Send token to backend API
      const response = await fetch('/api/notifications/register-token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${user.token}`,
        },
        body: JSON.stringify({
          token,
          platform: Platform.OS,
          deviceId: 'device-id', // You might want to get actual device ID
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to register token');
      }

      console.log('FCM token registered with backend');
    } catch (error) {
      console.log('Token registration error:', error);
    }
  }

  // Add notification listener
  addNotificationListener(callback) {
    this.notificationListeners.push(callback);
  }

  // Remove notification listener
  removeNotificationListener(callback) {
    this.notificationListeners = this.notificationListeners.filter(cb => cb !== callback);
  }

  // Get FCM token
  getFCMToken() {
    return this.fcmToken;
  }

  // Check if notifications are enabled
  async areNotificationsEnabled() {
    try {
      const settings = await notifee.getNotificationSettings();
      return settings.authorizationStatus === 1; // AUTHORIZED
    } catch (error) {
      console.log('Check notification settings error:', error);
      return false;
    }
  }

  // Open notification settings
  async openNotificationSettings() {
    try {
      await notifee.openNotificationSettings();
    } catch (error) {
      console.log('Open notification settings error:', error);
      // Fallback to app settings
      Linking.openSettings();
    }
  }

  // Clear all notifications
  async clearAllNotifications() {
    try {
      await notifee.cancelAllNotifications();
    } catch (error) {
      console.log('Clear notifications error:', error);
    }
  }

  // Get notification badge count (iOS)
  async getBadgeCount() {
    if (Platform.OS === 'ios') {
      try {
        return await notifee.getBadgeCount();
      } catch (error) {
        console.log('Get badge count error:', error);
        return 0;
      }
    }
    return 0;
  }

  // Set notification badge count (iOS)
  async setBadgeCount(count) {
    if (Platform.OS === 'ios') {
      try {
        await notifee.setBadgeCount(count);
      } catch (error) {
        console.log('Set badge count error:', error);
      }
    }
  }
}

// Create singleton instance
export const notificationService = new NotificationService();

// Export notification types for reference
export const NOTIFICATION_TYPES = {
  TRADE_UPDATE: 'trade_update',
  NEW_MESSAGE: 'new_message',
  SYSTEM_ALERT: 'system_alert',
};
