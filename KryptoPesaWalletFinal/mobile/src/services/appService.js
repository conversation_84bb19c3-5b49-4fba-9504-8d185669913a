import { Platform, PermissionsAndroid } from 'react-native';
import PushNotification from 'react-native-push-notification';
import messaging from '@react-native-firebase/messaging';

class AppService {
  async initializeApp() {
    try {
      // Initialize push notifications
      await this.initializePushNotifications();
      
      // Request permissions
      await this.requestPermissions();
      
      console.log('App initialized successfully');
    } catch (error) {
      console.log('App initialization error:', error);
    }
  }
  
  async initializePushNotifications() {
    try {
      // Request permission for iOS
      if (Platform.OS === 'ios') {
        const authStatus = await messaging().requestPermission();
        const enabled =
          authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
          authStatus === messaging.AuthorizationStatus.PROVISIONAL;
        
        if (!enabled) {
          console.log('Push notification permission denied');
          return;
        }
      }
      
      // Get FCM token
      const token = await messaging().getToken();
      console.log('FCM Token:', token);
      
      // Configure local notifications
      PushNotification.configure({
        onRegister: function (token) {
          console.log('TOKEN:', token);
        },
        
        onNotification: function (notification) {
          console.log('NOTIFICATION:', notification);
        },
        
        onAction: function (notification) {
          console.log('ACTION:', notification.action);
        },
        
        onRegistrationError: function(err) {
          console.error(err.message, err);
        },
        
        permissions: {
          alert: true,
          badge: true,
          sound: true,
        },
        
        popInitialNotification: true,
        requestPermissions: Platform.OS === 'ios',
      });
      
      // Handle background messages
      messaging().onMessage(async remoteMessage => {
        console.log('A new FCM message arrived!', JSON.stringify(remoteMessage));
        
        // Show local notification
        PushNotification.localNotification({
          title: remoteMessage.notification?.title,
          message: remoteMessage.notification?.body,
          playSound: true,
          soundName: 'default',
        });
      });
      
    } catch (error) {
      console.log('Push notification initialization error:', error);
    }
  }
  
  async requestPermissions() {
    try {
      if (Platform.OS === 'android') {
        // Request camera permission
        await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.CAMERA,
          {
            title: 'Camera Permission',
            message: 'KryptoPesa needs access to your camera to scan QR codes and take photos for trade verification.',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'OK',
          }
        );
        
        // Request storage permission
        await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
          {
            title: 'Storage Permission',
            message: 'KryptoPesa needs access to your storage to save images and documents.',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'OK',
          }
        );
      }
    } catch (error) {
      console.log('Permission request error:', error);
    }
  }
  
  showLocalNotification(title, message, data = {}) {
    PushNotification.localNotification({
      title,
      message,
      playSound: true,
      soundName: 'default',
      userInfo: data,
    });
  }
  
  scheduleNotification(title, message, date, data = {}) {
    PushNotification.localNotificationSchedule({
      title,
      message,
      date,
      playSound: true,
      soundName: 'default',
      userInfo: data,
    });
  }
  
  cancelAllNotifications() {
    PushNotification.cancelAllLocalNotifications();
  }
}

export const appService = new AppService();

export const initializeApp = () => {
  return appService.initializeApp();
};
