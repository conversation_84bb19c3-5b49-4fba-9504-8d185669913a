import { apiService } from './apiService';

class WalletService {
  async createWallet() {
    try {
      const response = await apiService.wallet.create();
      return response;
    } catch (error) {
      throw error;
    }
  }
  
  async importWallet(mnemonic) {
    try {
      const response = await apiService.wallet.import(mnemonic);
      return response;
    } catch (error) {
      throw error;
    }
  }
  
  async getWallet() {
    try {
      const response = await apiService.wallet.get();
      return response;
    } catch (error) {
      throw error;
    }
  }
  
  async refreshBalances() {
    try {
      const response = await apiService.wallet.refreshBalances();
      return response;
    } catch (error) {
      throw error;
    }
  }
  
  async getTransactionHistory(limit = 50, offset = 0) {
    try {
      const response = await apiService.wallet.getTransactions({ limit, offset });
      return response;
    } catch (error) {
      throw error;
    }
  }
  
  async verifyMnemonic(mnemonic) {
    try {
      const response = await apiService.wallet.verifyMnemonic(mnemonic);
      return response;
    } catch (error) {
      throw error;
    }
  }
  
  async markBackupCompleted() {
    try {
      const response = await apiService.wallet.markBackupCompleted();
      return response;
    } catch (error) {
      throw error;
    }
  }
  
  async getWalletStats() {
    try {
      const response = await apiService.wallet.getStats();
      return response;
    } catch (error) {
      throw error;
    }
  }
}

export const walletService = new WalletService();
