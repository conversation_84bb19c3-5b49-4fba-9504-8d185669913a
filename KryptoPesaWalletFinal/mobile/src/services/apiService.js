import axios from 'axios';
import EncryptedStorage from 'react-native-encrypted-storage';
import { showMessage } from 'react-native-flash-message';

// API Configuration
const API_BASE_URL = __DEV__ 
  ? 'http://localhost:3000/api' 
  : 'https://api.kryptopesa.com/api';

// Create axios instance
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  async (config) => {
    try {
      const token = await EncryptedStorage.getItem('auth_token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    } catch (error) {
      console.log('Error getting auth token:', error);
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const { response } = error;
    
    if (response?.status === 401) {
      // Token expired or invalid
      await EncryptedStorage.removeItem('auth_token');
      showMessage({
        message: 'Session Expired',
        description: 'Please log in again',
        type: 'warning',
      });
      // Navigate to login screen
      // This would be handled by the navigation service
    } else if (response?.status === 403) {
      showMessage({
        message: 'Access Denied',
        description: 'You do not have permission to perform this action',
        type: 'danger',
      });
    } else if (response?.status >= 500) {
      showMessage({
        message: 'Server Error',
        description: 'Something went wrong. Please try again later.',
        type: 'danger',
      });
    } else if (!response) {
      // Network error
      showMessage({
        message: 'Network Error',
        description: 'Please check your internet connection',
        type: 'danger',
      });
    }
    
    return Promise.reject(error);
  }
);

// API service methods
export const apiService = {
  // Auth endpoints
  auth: {
    login: (credentials) => apiClient.post('/auth/login', credentials),
    register: (userData) => apiClient.post('/auth/register', userData),
    getCurrentUser: () => apiClient.get('/auth/me'),
    logout: () => apiClient.post('/auth/logout'),
  },
  
  // Wallet endpoints
  wallet: {
    create: () => apiClient.post('/wallet/create'),
    import: (mnemonic) => apiClient.post('/wallet/import', { mnemonic }),
    get: () => apiClient.get('/wallet'),
    refreshBalances: () => apiClient.post('/wallet/balances/refresh'),
    getTransactions: (params) => apiClient.get('/wallet/transactions', { params }),
    verifyMnemonic: (mnemonic) => apiClient.post('/wallet/verify-mnemonic', { mnemonic }),
    markBackupCompleted: () => apiClient.post('/wallet/backup/complete'),
    getStats: () => apiClient.get('/wallet/stats'),
  },
  
  // Trade endpoints
  trades: {
    getActive: () => apiClient.get('/trades/active'),
    getHistory: (params) => apiClient.get('/trades/history', { params }),
    create: (tradeData) => apiClient.post('/trades', tradeData),
    get: (tradeId) => apiClient.get(`/trades/${tradeId}`),
    update: (tradeId, data) => apiClient.put(`/trades/${tradeId}`, data),
    confirmPayment: (tradeId, type) => apiClient.post(`/trades/${tradeId}/confirm-payment`, { type }),
    createDispute: (tradeId, reason) => apiClient.post(`/trades/${tradeId}/dispute`, { reason }),
  },
  
  // Offer endpoints
  offers: {
    getAll: (params) => apiClient.get('/offers', { params }),
    getMy: () => apiClient.get('/offers/my'),
    create: (offerData) => apiClient.post('/offers', offerData),
    update: (offerId, data) => apiClient.put(`/offers/${offerId}`, data),
    delete: (offerId) => apiClient.delete(`/offers/${offerId}`),
    respond: (offerId, amount) => apiClient.post(`/offers/${offerId}/respond`, { amount }),
  },
  
  // Chat endpoints
  chat: {
    getMessages: (tradeId, params) => apiClient.get(`/chat/${tradeId}/messages`, { params }),
    sendMessage: (tradeId, message) => apiClient.post(`/chat/${tradeId}/messages`, message),
    markAsRead: (tradeId, messageIds) => apiClient.post(`/chat/${tradeId}/read`, { messageIds }),
  },
  
  // User endpoints
  users: {
    getProfile: (userId) => apiClient.get(`/users/${userId}`),
    updateProfile: (data) => apiClient.put('/users/profile', data),
    getReputation: (userId) => apiClient.get(`/users/${userId}/reputation`),
    uploadAvatar: (formData) => apiClient.post('/users/avatar', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    }),
  },
  
  // Utility endpoints
  utils: {
    getCurrencyRates: () => apiClient.get('/utils/rates'),
    getCountries: () => apiClient.get('/utils/countries'),
    uploadFile: (formData) => apiClient.post('/utils/upload', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    }),
  },
};

export default apiClient;
