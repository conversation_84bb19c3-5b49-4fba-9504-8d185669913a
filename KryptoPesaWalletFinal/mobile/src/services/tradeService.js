import { apiService } from './apiService';

class TradeService {
  async getActiveTrades() {
    try {
      const response = await apiService.trades.getActive();
      return response;
    } catch (error) {
      throw error;
    }
  }
  
  async createTrade(tradeData) {
    try {
      const response = await apiService.trades.create(tradeData);
      return response;
    } catch (error) {
      throw error;
    }
  }
  
  async getTrade(tradeId) {
    try {
      const response = await apiService.trades.get(tradeId);
      return response;
    } catch (error) {
      throw error;
    }
  }
  
  async confirmPayment(tradeId, type) {
    try {
      const response = await apiService.trades.confirmPayment(tradeId, type);
      return response;
    } catch (error) {
      throw error;
    }
  }
  
  async createDispute(tradeId, reason) {
    try {
      const response = await apiService.trades.createDispute(tradeId, reason);
      return response;
    } catch (error) {
      throw error;
    }
  }
}

export const tradeService = new TradeService();
