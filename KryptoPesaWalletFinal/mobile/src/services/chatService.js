import { apiService } from './apiService';

class ChatService {
  async getMessages(tradeId, limit = 50, offset = 0) {
    try {
      const response = await apiService.chat.getMessages(tradeId, { limit, offset });
      return response;
    } catch (error) {
      throw error;
    }
  }
  
  async sendMessage(tradeId, content, type = 'text', attachments = []) {
    try {
      const response = await apiService.chat.sendMessage(tradeId, {
        content,
        type,
        attachments
      });
      return response;
    } catch (error) {
      throw error;
    }
  }
  
  async markAsRead(tradeId, messageIds = []) {
    try {
      const response = await apiService.chat.markAsRead(tradeId, { messageIds });
      return response;
    } catch (error) {
      throw error;
    }
  }
  
  async getUnreadCount() {
    try {
      const response = await apiService.chat.getUnreadCount();
      return response;
    } catch (error) {
      throw error;
    }
  }
}

export const chatService = new ChatService();
