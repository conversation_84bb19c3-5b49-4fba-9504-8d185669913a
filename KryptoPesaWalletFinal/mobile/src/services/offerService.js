import { apiService } from './apiService';

class OfferService {
  async getOffers(filters = {}) {
    try {
      const response = await apiService.offers.getAll(filters);
      return response;
    } catch (error) {
      throw error;
    }
  }

  async getMyOffers() {
    try {
      const response = await apiService.offers.getMy();
      return response;
    } catch (error) {
      throw error;
    }
  }

  async getOffer(offerId) {
    try {
      const response = await apiService.offers.get(offerId);
      return response;
    } catch (error) {
      throw error;
    }
  }

  async createOffer(offerData) {
    try {
      const response = await apiService.offers.create(offerData);
      return response;
    } catch (error) {
      throw error;
    }
  }

  async updateOffer(offerId, updates) {
    try {
      const response = await apiService.offers.update(offerId, updates);
      return response;
    } catch (error) {
      throw error;
    }
  }

  async deleteOffer(offerId) {
    try {
      const response = await apiService.offers.delete(offerId);
      return response;
    } catch (error) {
      throw error;
    }
  }

  async respondToOffer(offerId, amount, paymentMethod) {
    try {
      const response = await apiService.offers.respond(offerId, { amount, paymentMethod });
      return response;
    } catch (error) {
      throw error;
    }
  }

  async searchOffers(criteria) {
    try {
      const response = await apiService.offers.search(criteria);
      return response;
    } catch (error) {
      throw error;
    }
  }
}

export const offerService = new OfferService();
