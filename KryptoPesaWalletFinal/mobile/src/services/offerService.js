import { apiService } from './apiService';

class OfferService {
  async getOffers(filters = {}) {
    try {
      const response = await apiService.offers.getAll(filters);
      return response;
    } catch (error) {
      throw error;
    }
  }
  
  async getMyOffers() {
    try {
      const response = await apiService.offers.getMy();
      return response;
    } catch (error) {
      throw error;
    }
  }
  
  async createOffer(offerData) {
    try {
      const response = await apiService.offers.create(offerData);
      return response;
    } catch (error) {
      throw error;
    }
  }
  
  async respondToOffer(offerId, amount) {
    try {
      const response = await apiService.offers.respond(offerId, amount);
      return response;
    } catch (error) {
      throw error;
    }
  }
}

export const offerService = new OfferService();
