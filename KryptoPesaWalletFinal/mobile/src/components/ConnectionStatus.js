import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Animated } from 'react-native';
import { Text, Chip, IconButton } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { socketService, CONNECTION_STATES } from '../services/socketService';
import { theme } from '../utils/theme';

const ConnectionStatus = ({ style, showDetails = false }) => {
  const [connectionState, setConnectionState] = useState(CONNECTION_STATES.DISCONNECTED);
  const [reconnectInfo, setReconnectInfo] = useState(null);
  const [pulseAnim] = useState(new Animated.Value(1));

  useEffect(() => {
    const handleConnectionChange = (status, data) => {
      setConnectionState(status);
      
      if (status === CONNECTION_STATES.RECONNECTING) {
        setReconnectInfo(data);
        startPulseAnimation();
      } else {
        setReconnectInfo(null);
        stopPulseAnimation();
      }
    };

    socketService.addConnectionListener(handleConnectionChange);
    
    // Set initial state
    setConnectionState(socketService.getConnectionState());

    return () => {
      socketService.removeConnectionListener(handleConnectionChange);
      stopPulseAnimation();
    };
  }, []);

  const startPulseAnimation = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 0.5,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  const stopPulseAnimation = () => {
    pulseAnim.stopAnimation();
    pulseAnim.setValue(1);
  };

  const getStatusConfig = () => {
    switch (connectionState) {
      case CONNECTION_STATES.CONNECTED:
        return {
          icon: 'wifi',
          color: theme.colors.success,
          text: 'Connected',
          chipColor: theme.colors.successContainer,
          textColor: theme.colors.onSuccessContainer,
        };
      case CONNECTION_STATES.CONNECTING:
        return {
          icon: 'wifi-sync',
          color: theme.colors.warning,
          text: 'Connecting...',
          chipColor: theme.colors.warningContainer,
          textColor: theme.colors.onWarningContainer,
        };
      case CONNECTION_STATES.RECONNECTING:
        return {
          icon: 'wifi-sync',
          color: theme.colors.warning,
          text: reconnectInfo 
            ? `Reconnecting... (${reconnectInfo.attempt}/5)`
            : 'Reconnecting...',
          chipColor: theme.colors.warningContainer,
          textColor: theme.colors.onWarningContainer,
        };
      case CONNECTION_STATES.FAILED:
        return {
          icon: 'wifi-off',
          color: theme.colors.error,
          text: 'Connection Failed',
          chipColor: theme.colors.errorContainer,
          textColor: theme.colors.onErrorContainer,
        };
      case CONNECTION_STATES.ERROR:
        return {
          icon: 'wifi-alert',
          color: theme.colors.error,
          text: 'Connection Error',
          chipColor: theme.colors.errorContainer,
          textColor: theme.colors.onErrorContainer,
        };
      default:
        return {
          icon: 'wifi-off',
          color: theme.colors.onSurfaceVariant,
          text: 'Offline',
          chipColor: theme.colors.surfaceVariant,
          textColor: theme.colors.onSurfaceVariant,
        };
    }
  };

  const handleRetryConnection = () => {
    socketService.forceReconnect();
  };

  const config = getStatusConfig();
  const isConnecting = connectionState === CONNECTION_STATES.CONNECTING || 
                      connectionState === CONNECTION_STATES.RECONNECTING;

  if (!showDetails && connectionState === CONNECTION_STATES.CONNECTED) {
    // Don't show anything when connected and details not requested
    return null;
  }

  return (
    <View style={[styles.container, style]}>
      <Chip
        mode="outlined"
        style={[styles.chip, { backgroundColor: config.chipColor }]}
        textStyle={{ color: config.textColor, fontSize: 12 }}
        icon={() => (
          <Animated.View style={{ opacity: isConnecting ? pulseAnim : 1 }}>
            <Icon 
              name={config.icon} 
              size={16} 
              color={config.color} 
            />
          </Animated.View>
        )}
      >
        {config.text}
      </Chip>
      
      {(connectionState === CONNECTION_STATES.FAILED || 
        connectionState === CONNECTION_STATES.ERROR) && (
        <IconButton
          icon="refresh"
          size={20}
          onPress={handleRetryConnection}
          style={styles.retryButton}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  chip: {
    height: 28,
  },
  retryButton: {
    marginLeft: theme.spacing.xs,
  },
});

export default ConnectionStatus;
