import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
} from 'react-native';
import {
  Modal,
  Portal,
  Text,
  Button,
  SegmentedButtons,
  TextInput,
  Divider,
  Chip,
} from 'react-native-paper';
import { theme } from '../utils/theme';

const FilterModal = ({ visible, onDismiss, filters, onFiltersChange }) => {
  const [localFilters, setLocalFilters] = useState(filters);

  const handleApplyFilters = () => {
    onFiltersChange(localFilters);
    onDismiss();
  };

  const handleResetFilters = () => {
    const resetFilters = {
      type: 'all',
      cryptocurrency: 'all',
      fiatCurrency: 'all',
      paymentMethod: 'all',
      minAmount: '',
      maxAmount: '',
    };
    setLocalFilters(resetFilters);
  };

  const updateFilter = (key, value) => {
    setLocalFilters(prev => ({ ...prev, [key]: value }));
  };

  const tradeTypes = [
    { value: 'all', label: 'All' },
    { value: 'buy', label: 'Buy' },
    { value: 'sell', label: 'Sell' },
  ];

  const cryptocurrencies = [
    { value: 'all', label: 'All' },
    { value: 'USDT', label: 'USDT' },
    { value: 'USDC', label: 'USDC' },
    { value: 'BTC', label: 'BTC' },
    { value: 'ETH', label: 'ETH' },
    { value: 'DAI', label: 'DAI' },
  ];

  const fiatCurrencies = [
    { value: 'KES', label: 'KES' },
    { value: 'TZS', label: 'TZS' },
    { value: 'UGX', label: 'UGX' },
    { value: 'RWF', label: 'RWF' },
    { value: 'USD', label: 'USD' },
  ];

  const paymentMethods = [
    { value: 'all', label: 'All' },
    { value: 'mobile_money', label: 'Mobile Money' },
    { value: 'bank_transfer', label: 'Bank Transfer' },
    { value: 'cash', label: 'Cash' },
    { value: 'other', label: 'Other' },
  ];

  return (
    <Portal>
      <Modal
        visible={visible}
        onDismiss={onDismiss}
        contentContainerStyle={styles.modalContainer}
      >
        <ScrollView showsVerticalScrollIndicator={false}>
          <Text style={styles.modalTitle}>Filter Offers</Text>

          {/* Trade Type */}
          <View style={styles.filterSection}>
            <Text style={styles.sectionTitle}>Trade Type</Text>
            <SegmentedButtons
              value={localFilters.type}
              onValueChange={(value) => updateFilter('type', value)}
              buttons={tradeTypes}
              style={styles.segmentedButtons}
            />
          </View>

          <Divider style={styles.divider} />

          {/* Cryptocurrency */}
          <View style={styles.filterSection}>
            <Text style={styles.sectionTitle}>Cryptocurrency</Text>
            <View style={styles.chipContainer}>
              {cryptocurrencies.map((crypto) => (
                <Chip
                  key={crypto.value}
                  selected={localFilters.cryptocurrency === crypto.value}
                  onPress={() => updateFilter('cryptocurrency', crypto.value)}
                  style={styles.chip}
                >
                  {crypto.label}
                </Chip>
              ))}
            </View>
          </View>

          <Divider style={styles.divider} />

          {/* Fiat Currency */}
          <View style={styles.filterSection}>
            <Text style={styles.sectionTitle}>Fiat Currency</Text>
            <View style={styles.chipContainer}>
              {fiatCurrencies.map((currency) => (
                <Chip
                  key={currency.value}
                  selected={localFilters.fiatCurrency === currency.value}
                  onPress={() => updateFilter('fiatCurrency', currency.value)}
                  style={styles.chip}
                >
                  {currency.label}
                </Chip>
              ))}
            </View>
          </View>

          <Divider style={styles.divider} />

          {/* Payment Method */}
          <View style={styles.filterSection}>
            <Text style={styles.sectionTitle}>Payment Method</Text>
            <View style={styles.chipContainer}>
              {paymentMethods.map((method) => (
                <Chip
                  key={method.value}
                  selected={localFilters.paymentMethod === method.value}
                  onPress={() => updateFilter('paymentMethod', method.value)}
                  style={styles.chip}
                >
                  {method.label}
                </Chip>
              ))}
            </View>
          </View>

          <Divider style={styles.divider} />

          {/* Amount Range */}
          <View style={styles.filterSection}>
            <Text style={styles.sectionTitle}>Amount Range</Text>
            <View style={styles.amountInputs}>
              <TextInput
                label="Min Amount"
                value={localFilters.minAmount}
                onChangeText={(value) => updateFilter('minAmount', value)}
                keyboardType="numeric"
                style={styles.amountInput}
                mode="outlined"
                dense
              />
              <TextInput
                label="Max Amount"
                value={localFilters.maxAmount}
                onChangeText={(value) => updateFilter('maxAmount', value)}
                keyboardType="numeric"
                style={styles.amountInput}
                mode="outlined"
                dense
              />
            </View>
          </View>

          {/* Action Buttons */}
          <View style={styles.actionButtons}>
            <Button
              mode="outlined"
              onPress={handleResetFilters}
              style={styles.resetButton}
            >
              Reset
            </Button>
            <Button
              mode="contained"
              onPress={handleApplyFilters}
              style={styles.applyButton}
            >
              Apply Filters
            </Button>
          </View>
        </ScrollView>
      </Modal>
    </Portal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    backgroundColor: theme.colors.surface,
    margin: theme.spacing.lg,
    borderRadius: theme.roundness,
    padding: theme.spacing.lg,
    maxHeight: '80%',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: theme.spacing.lg,
    color: theme.colors.onSurface,
  },
  filterSection: {
    marginBottom: theme.spacing.md,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: theme.spacing.sm,
    color: theme.colors.onSurface,
  },
  segmentedButtons: {
    marginBottom: theme.spacing.sm,
  },
  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: theme.spacing.sm,
  },
  chip: {
    marginBottom: theme.spacing.xs,
  },
  divider: {
    marginVertical: theme.spacing.md,
  },
  amountInputs: {
    flexDirection: 'row',
    gap: theme.spacing.sm,
  },
  amountInput: {
    flex: 1,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: theme.spacing.lg,
    gap: theme.spacing.sm,
  },
  resetButton: {
    flex: 1,
  },
  applyButton: {
    flex: 1,
  },
});

export default FilterModal;
