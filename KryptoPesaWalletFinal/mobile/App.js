import React, { useEffect } from 'react';
import { LogBox, Platform } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { NavigationContainer } from '@react-navigation/native';
import { Provider as PaperProvider } from 'react-native-paper';
import FlashMessage from 'react-native-flash-message';
import { GestureHandlerRootView } from 'react-native-gesture-handler';

import { store, persistor } from './src/store';
import AppNavigator from './src/navigation/AppNavigator';
import { theme } from './src/utils/theme';
import LoadingScreen from './src/components/LoadingScreen';
import { initializeApp } from './src/services/appService';

// Conditional import for notification service (not available in Expo Go)
let notificationService = null;
if (Platform.OS !== 'web') {
  try {
    notificationService = require('./src/services/notificationService').notificationService;
  } catch (error) {
    console.log('Notification service not available in Expo Go');
  }
}

// Ignore specific warnings for demo
LogBox.ignoreLogs([
  'Non-serializable values were found in the navigation state',
  'Remote debugger',
]);

const App = () => {
  useEffect(() => {
    // Initialize app services
    initializeApp();

    // Initialize notification service (if available)
    if (notificationService) {
      notificationService.initialize();
    }
  }, []);

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <Provider store={store}>
        <PersistGate loading={<LoadingScreen />} persistor={persistor}>
          <PaperProvider theme={theme}>
            <NavigationContainer>
              <StatusBar style="light" />
              <AppNavigator />
              <FlashMessage position="top" />
            </NavigationContainer>
          </PaperProvider>
        </PersistGate>
      </Provider>
    </GestureHandlerRootView>
  );
};

export default App;
